// app/providers.tsx
"use client";

import { RouteLoader } from "@/components/RouteLoader";
import { ReduxProvider } from "@/store/provider";
import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";
import { ToastContainer } from "react-toastify";
import { SnackbarProvider } from "@/contexts/SnackbarContext";

export function Providers({ children }: { children: ReactNode }) {
    return (
        <SessionProvider>
            <ReduxProvider>
                <SnackbarProvider>
                    <RouteLoader />
                    {children}
                </SnackbarProvider>
            </ReduxProvider >
            <ToastContainer position="top-center" />
        </SessionProvider>
    )
}
