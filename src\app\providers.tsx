// app/providers.tsx
"use client";

import { RouteLoader } from "@/components/RouteLoader";
import { ReduxProvider } from "@/store/provider";
import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";
import { ToastContainer } from "react-toastify";

export function Providers({ children }: { children: ReactNode }) {
    return (
        <SessionProvider>
            <ReduxProvider>
                <RouteLoader />
                {children}
            </ReduxProvider >
            <ToastContainer position="top-center" />;
        </SessionProvider>
    )
}
