import { io, Socket } from 'socket.io-client';
import type { MessageData, IncomingMessage } from '@/types/socket';

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private messageHandlers: Array<(msg: IncomingMessage) => void> = [];
  private connectionHandlers: Array<(connected: boolean) => void> = [];

  constructor() {
    this.initializeSocket();
  }

  private initializeSocket() {
    const userId = sessionStorage.getItem('userId');
    const token = localStorage.getItem('token');
    if (!userId || !token) return;

    this.socket = io('https://api.engageathlete.com', {
      path: '/socket.io/',
      auth: { id: userId, token },
      transports: ['polling', 'websocket'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
    });

    this.socket.on('connect', () => {
      this.isConnected = true;
      this.connectionHandlers.forEach(fn => fn(true));
    });

    this.socket.on('disconnect', () => {
      this.isConnected = false;
      this.connectionHandlers.forEach(fn => fn(false));
    });

    this.socket.on('connect_error', () => {
      this.isConnected = false;
      this.connectionHandlers.forEach(fn => fn(false));
    });

    ['message', 'new_message', 'messageCreated', 'receiveMessage'].forEach(evt => {
      this.socket!.on(evt, (msg: IncomingMessage) => {
        this.messageHandlers.forEach(fn => fn(msg));
      });
    });
  }

  sendMessage(data: MessageData): Promise<boolean> {
    return new Promise(resolve => {
      if (!this.socket || !this.isConnected) return resolve(false);

      const room = `${data.senderId}-${data.receiverId}`;
      this.socket.emit('join', room);

      this.socket.emit('createMessage', {
        ...data,
        room,
        createdAt: new Date().toISOString(),
      }, (ack: any) => {
        resolve(ack?.success !== false);
      });
    });
  }

  joinConversation(id: string) {
    this.socket?.emit('join', `/messages/${id}`);
  }

  leaveConversation(id: string) {
    this.socket?.emit('leave', `/messages/${id}`);
  }

  onMessage(fn: (msg: IncomingMessage) => void) {
    this.messageHandlers.push(fn);
    return () => this.messageHandlers = this.messageHandlers.filter(h => h !== fn);
  }

  onConnectionChange(fn: (connected: boolean) => void) {
    this.connectionHandlers.push(fn);
    return () => this.connectionHandlers = this.connectionHandlers.filter(h => h !== fn);
  }

  getConnectionStatus() { return this.isConnected; }
  reconnect() { this.socket?.disconnect(); this.initializeSocket(); }
  disconnect() { this.socket?.disconnect(); this.isConnected = false; }
  sendTyping(receiverId: string, isTyping: boolean) {
    this.socket?.emit('typing', { receiverId, isTyping });
  }
}

export default new SocketService();
export type { MessageData, IncomingMessage };
