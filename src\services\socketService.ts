import { io, Socket } from 'socket.io-client';

interface MessageData {
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: Date;
  attachment?: {
    file: File;
    type: 'image' | 'document' | 'pdf';
    url: string;
    name: string;
    size: number;
  };
}

interface IncomingMessage {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: Date;
  attachment?: {
    file: File;
    type: 'image' | 'document' | 'pdf';
    url: string;
    name: string;
    size: number;
  };
}

class SocketService {
  private socket: Socket | null = null;
  private isConnected: boolean = false;
  private messageHandlers: ((message: IncomingMessage) => void)[] = [];
  private connectionHandlers: ((connected: boolean) => void)[] = [];

  constructor() {
    this.initializeSocket();
  }

  private initializeSocket() {
    try {
      // Get authentication data from localStorage
      const userId = sessionStorage.getItem("userId");
      const token = JSON.parse(localStorage.getItem("token") || "null");

      if (!userId || !token) {
        console.warn('Socket authentication data not found. UserId:', userId, 'Token:', !!token);
        return;
      }

      console.log('Initializing socket connection with userId:', userId);

      // Create socket connection
      this.socket = io('https://api.engageathlete.com/socket.io/', {
        auth: {
          id: userId,
          password: token // Using token as password as per user preference
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to initialize socket:', error);
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('Socket connected:', this.socket?.id);
      this.isConnected = true;
      this.notifyConnectionHandlers(true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnected = false;
      this.notifyConnectionHandlers(false);
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnected = false;
      this.notifyConnectionHandlers(false);
    });

    // Message events
    this.socket.on('message', (data: IncomingMessage) => {
      console.log('Received message:', data);
      this.notifyMessageHandlers(data);
    });

    this.socket.on('new_message', (data: IncomingMessage) => {
      console.log('Received new message:', data);
      this.notifyMessageHandlers(data);
    });

    // Typing events (optional for future enhancement)
    this.socket.on('user_typing', (data: { userId: string; isTyping: boolean }) => {
      console.log('User typing:', data);
    });
  }

  // Send a message through socket
  public sendMessage(messageData: MessageData): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.socket || !this.isConnected) {
        console.error('Socket not connected');
        resolve(false);
        return;
      }

      try {
        this.socket.emit('send_message', messageData, (acknowledgment: any) => {
          if (acknowledgment?.success) {
            console.log('Message sent successfully');
            resolve(true);
          } else {
            console.error('Failed to send message:', acknowledgment?.error);
            resolve(false);
          }
        });
      } catch (error) {
        console.error('Error sending message:', error);
        resolve(false);
      }
    });
  }

  // Join a conversation room
  public joinConversation(conversationId: string) {
    if (!this.socket || !this.isConnected) {
      console.error('Socket not connected');
      return;
    }

    this.socket.emit('join_conversation', { conversationId });
    console.log('Joined conversation:', conversationId);
  }

  // Leave a conversation room
  public leaveConversation(conversationId: string) {
    if (!this.socket || !this.isConnected) {
      console.error('Socket not connected');
      return;
    }

    this.socket.emit('leave_conversation', { conversationId });
    console.log('Left conversation:', conversationId);
  }

  // Subscribe to incoming messages
  public onMessage(handler: (message: IncomingMessage) => void) {
    this.messageHandlers.push(handler);
    
    // Return unsubscribe function
    return () => {
      this.messageHandlers = this.messageHandlers.filter(h => h !== handler);
    };
  }

  // Subscribe to connection status changes
  public onConnectionChange(handler: (connected: boolean) => void) {
    this.connectionHandlers.push(handler);
    
    // Return unsubscribe function
    return () => {
      this.connectionHandlers = this.connectionHandlers.filter(h => h !== handler);
    };
  }

  private notifyMessageHandlers(message: IncomingMessage) {
    this.messageHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('Error in message handler:', error);
      }
    });
  }

  private notifyConnectionHandlers(connected: boolean) {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        console.error('Error in connection handler:', error);
      }
    });
  }

  // Get connection status
  public getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // Reconnect socket
  public reconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
    this.initializeSocket();
  }

  // Disconnect socket
  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.notifyConnectionHandlers(false);
    }
  }

  // Send typing indicator
  public sendTyping(receiverId: string, isTyping: boolean) {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit('typing', {
      receiverId,
      isTyping
    });
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
export type { MessageData, IncomingMessage };
