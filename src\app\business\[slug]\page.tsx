"use client";
import ClientGuard from "@/components/ClientGuard";
import CustomDropdown from "@/components/common/DropDown";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllLocations,
  fetchAllSpecialities,
  fetchAllSports,
  fetchAllStates,
} from "@/store/slices/commonSlice";
import { fetchAllAnnouncements } from "@/store/slices/org-announcement/announcement";
import { fetchOrgProfile } from "@/store/slices/org-announcement/sportsBusiness";
import { Globe, Mail, PencilLine, Phone } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { FaInstagram, FaXTwitter, FaYoutube } from "react-icons/fa6";
import { useDispatch, useSelector } from "react-redux";
import Select from "react-select";
import SpotLights from "./spotlight";
import { useRouter } from "next/navigation";

const BusinessProfile = () => {
  const [orgName, setOrgName] = useState("Organization Name");
  const router = useRouter()
  const [editingName, setEditingName] = useState(false);
  const [blurb, setBlurb] = useState(
    "We are a dynamic organization offering hybrid solutions in tech, consulting, and innovation. Join us to revolutionize the digital world."
  );
  const [about, setAbout] = useState(
    "This section can include more details about the organization..."
  );
  const [serviceChannel, setServiceChannel] = useState();
  const [contact, setContact] = useState({});
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [businessName, setBusinessName] = useState("");
  const [isEmailVerified, setIsEmailVerified] = useState<boolean>(false);
  // orgProfile?.[0]?.user?.isEmailVerified ?? false
  const [resendCooldown, setResendCooldown] = useState<number>(0);
  const [resendMessage, setResendMessage] = useState<string>("");

  const { orgProfile, selectedState, selectedLocations, selectedSport, selectedSpecialities } = useSelector((state: RootState) => state.orgProfile);
  const { allAnnouncements, } = useSelector(
    (state: RootState) => state.announcementDashboard
  );

  const {
    allStatesList,
    allLocationsList,
    allSportsList,
    allSpecilitiesList,
  } = useSelector((state: RootState) => state.commonSlice);

  const dispatch = useDispatch<AppDispatch>();

  const serviceChannelOptions: any = [
    { label: "In Person", value: "In Person" },
    { label: "Online", value: "Online" },
    { label: "Hybrid", value: "Hybrid" },
  ];

  const formatToDayMonthYear = (isoDate: string): string => {
    const date = new Date(isoDate);
    const options: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "short",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  const handleResendVerification = () => {
    // Simulate API call
    setResendMessage("Verification email sent!");
    setResendCooldown(30); // 30 seconds cooldown
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleChange = (e?: any) => {
    setServiceChannel(e.target.value);
  };

  const socialMediaLinks = [
    {
      id: "instagram",
      name: "Instagram",
      icon: <FaInstagram className="text-pink-500 text-xl" />,
      link: orgProfile?.[0]?.instagramLink,
    },
    {
      id: "twitter",
      name: "X",
      icon: <FaXTwitter className="text-black text-xl" />,
      link: orgProfile?.[0]?.twitterLink,
    },
    {
      id: "youtube",
      name: "YouTube",
      icon: <FaYoutube className="text-red-600 text-xl" />,
      link: "YouTube Link",
    },
  ];
  const businessTypesList = [
    { value: 1, label: "Sports Academy" },
    { value: 2, label: "Sports Club" },
    { value: 3, label: "Sports Training" },
    { value: 4, label: "Sports Facility" },
    { value: 5, label: "Sports League" },
    { value: 6, label: "Sports Fitness" },
    { value: 7, label: "Sports Health & Wellness" },
    { value: 8, label: "Sports Media & Broadcasting" },
    { value: 9, label: "Sports Marketing" },
    { value: 10, label: "Sports Equipment" },
    { value: 11, label: "Sports Education" },
    { value: 12, label: "Sports Merchandise" },
    { value: 13, label: "Sports Events Management" },
    { value: 14, label: "Sports Associations" },
    { value: 15, label: "Non-Profit Organizations" },
    { value: 16, label: "Sports Sponsorship" },
    { value: 17, label: "Educational Institution" },
  ];

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (resendCooldown > 0) {
      timer = setTimeout(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [resendCooldown]);

  useEffect(() => {
    dispatch(fetchOrgProfile());
  }, []);

  useEffect(() => {
    dispatch(fetchAllStates());
  }, [dispatch]);

  useEffect(() => {
    selectedState?.value && dispatch(fetchAllLocations(selectedState?.value));
  }, [selectedState?.value, dispatch]);

  useEffect(() => {
    dispatch(fetchAllSports());
  }, [dispatch]);

  useEffect(() => {
    if (selectedSport?.value) {
      dispatch(fetchAllSpecialities(selectedSport.value));
    }
  }, [dispatch, selectedSport?.value]);

  useEffect(() => {
    dispatch(fetchAllAnnouncements());
  }, [dispatch]);

  console.log(
    "allAnnouncementsallAnnouncements",
    orgProfile
  );

  return (
    <ClientGuard allowedRoles={[4]}>
      {/* URL Preview Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 py-6 px-4 border-b border-gray-200">
        <div className="max-w-screen-xl mx-auto">
          <div className="flex justify-center items-center flex-wrap text-sm md:text-base font-medium">
            <div className="bg-white rounded-lg px-4 py-2 shadow-sm border border-gray-200 flex items-center gap-1">
              <span className="text-gray-600">www.connectathlete.com/</span>
              <span className="text-blue-600 font-semibold">
                {orgProfile?.[0]?.businessType?.businessName}/
              </span>
              <span className="text-gray-600">{orgProfile?.[0]?.user?.id}-</span>
              <input
                type="text"
                value={businessName}
                onChange={(e) => setBusinessName(e.target.value)}
                placeholder="BusinessName"
                className="border-none focus:outline-none text-green-600 bg-transparent font-semibold w-[160px] px-1"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="min-h-screen bg-gray-50">
        <div className="max-w-screen-xl mx-auto w-full p-6 space-y-8">
          {/* Top Section: Profile + Announcement */}
          <div className="flex flex-col lg:flex-row gap-8 w-full">
            {/* Left: Profile */}
            <div className="flex flex-col flex-[2] gap-8 bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              {/* Profile Header */}
              <div className="flex flex-col sm:flex-row gap-6 items-start">
                <div className="relative group">
                  <div className="w-28 h-28 rounded-2xl border-4 border-blue-100 overflow-hidden bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                    {orgProfile?.[0]?.user?.profileImg ? (
                      <Image
                        src={orgProfile[0].user.profileImg}
                        alt="Profile"
                        width={112}
                        height={112}
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-14 h-14 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0zm6 1a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    )}
                  </div>
                  <div className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-semibold shadow-md">
                    Premium
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="flex items-center gap-3">
                    {editingName ? (
                      <input
                        className="text-3xl font-bold border-b-2 border-blue-300 focus:outline-none focus:border-blue-500 w-full bg-transparent"
                        value={orgProfile?.[0]?.organizationName}
                        onChange={(e) => setOrgName(e.target.value)}
                        onBlur={() => setEditingName(false)}
                        autoFocus
                      />
                    ) : (
                      <h2
                        className="text-3xl font-bold text-gray-800 cursor-pointer hover:text-blue-600 transition-colors"
                        onClick={() => setEditingName(true)}
                      >
                        {orgProfile?.[0]?.organizationName}
                      </h2>
                    )}
                    <PencilLine className="w-5 h-5 text-gray-400 cursor-pointer hover:text-blue-500 transition-colors" />
                  </div>

                  <div className="relative">
                    <textarea
                      className="text-sm p-4 border-2 border-gray-200 rounded-xl w-full font-medium resize-none focus:border-blue-300 focus:outline-none transition-colors bg-gray-50 hover:bg-white"
                      value={blurb}
                      onChange={(e) => setBlurb(e.target.value)}
                      placeholder="Tell us about your organization..."
                      rows={3}
                    />
                    <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                      {blurb.length}/200
                    </div>
                  </div>
                </div>
              </div>

              {/* Business Configuration */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">
                  Business Configuration
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Business Type</label>
                    <CustomDropdown
                      options={businessTypesList.map((item: any) => item.label)}
                      selectedValue={orgProfile?.[0]?.businessType?.businessName}
                      handleChange={() => { }}
                      placeholder="Select a Business Type"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Service Channel</label>
                    <CustomDropdown
                      options={serviceChannelOptions.map((item: any) => item.label)}
                      selectedValue={serviceChannel || ""}
                      handleChange={handleChange}
                      placeholder="Select Service Channel"
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">
                  Contact Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">First Name</label>
                    <input
                      className="p-3 border-2 border-gray-200 rounded-xl w-full focus:border-blue-300 focus:outline-none transition-colors"
                      value={orgProfile?.[0]?.user?.firstName}
                      onChange={(e) =>
                        setContact({ ...contact, firstName: e.target.value })
                      }
                      placeholder="Enter first name"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Email Address</label>
                    <div className="relative">
                      <div className="flex items-center gap-3 border-2 border-gray-200 rounded-xl p-3 w-full focus-within:border-blue-300 transition-colors">
                        <Mail className="w-5 h-5 text-gray-500" />
                        <input
                          className="w-full focus:outline-none"
                          value={orgProfile?.[0]?.user?.email}
                          onChange={(e) =>
                            setContact({ ...contact, email: e.target.value })
                          }
                          placeholder="Enter email address"
                        />
                      </div>

                      {!isEmailVerified && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <div className="flex items-start gap-2">
                            <div className="w-4 h-4 rounded-full bg-red-500 mt-0.5 flex-shrink-0"></div>
                            <div className="space-y-2">
                              <span className="text-sm text-red-700 font-medium">Email not verified</span>
                              <button
                                onClick={handleResendVerification}
                                disabled={resendCooldown > 0}
                                className={`px-4 py-2 rounded-lg text-white text-sm font-medium transition-colors ${resendCooldown > 0
                                  ? "bg-gray-400 cursor-not-allowed"
                                  : "bg-blue-600 hover:bg-blue-700"
                                  }`}
                              >
                                {resendCooldown > 0
                                  ? `Resend in ${resendCooldown}s`
                                  : "Send Verification Email"}
                              </button>
                              {resendMessage && (
                                <div className="text-sm text-green-600 font-medium">{resendMessage}</div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Last Name</label>
                    <input
                      className="p-3 border-2 border-gray-200 rounded-xl w-full focus:border-blue-300 focus:outline-none transition-colors"
                      value={orgProfile?.[0]?.user?.lastName}
                      onChange={(e) =>
                        setContact({ ...contact, lastName: e.target.value })
                      }
                      placeholder="Enter last name"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Phone Number</label>
                    <div className="flex items-center gap-3 border-2 border-gray-200 rounded-xl p-3 w-full focus-within:border-blue-300 transition-colors">
                      <Phone className="w-5 h-5 text-gray-500" />
                      <input
                        className="w-full focus:outline-none"
                        value={orgProfile?.[0]?.phone}
                        onChange={(e) =>
                          setContact({ ...contact, phone: e.target.value })
                        }
                        placeholder="Enter phone number"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Title/Position</label>
                    <input
                      className="p-3 border-2 border-gray-200 rounded-xl w-full focus:border-blue-300 focus:outline-none transition-colors"
                      value={orgProfile?.[0]?.title}
                      onChange={(e) =>
                        setContact({ ...contact, title: e.target.value })
                      }
                      placeholder="Enter your title"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Website</label>
                    <div className="flex items-center gap-3 border-2 border-gray-200 rounded-xl p-3 w-full focus-within:border-blue-300 transition-colors">
                      <Globe className="w-5 h-5 text-gray-500" />
                      <input
                        className="w-full focus:outline-none"
                        value={orgProfile?.[0]?.websiteLink}
                        onChange={(e) =>
                          setContact({ ...contact, link: e.target.value })
                        }
                        placeholder="Enter website URL"
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-xl border border-gray-200">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                    <label className="text-sm font-medium text-gray-700">Last Updated</label>
                    <input
                      className="flex-1 bg-transparent focus:outline-none text-sm text-gray-600"
                      value={formatToDayMonthYear(
                        orgProfile?.[0]?.user?.lastTermsAcceptedAt
                      )}
                      placeholder="Last Updated Date"
                      readOnly
                    />
                  </div>
                </div>
              </div>

              {/* About Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">
                  About {orgProfile?.[0]?.organizationName}
                </h3>
                <div className="relative">
                  <textarea
                    className="p-4 border-2 border-gray-200 rounded-xl w-full text-sm font-medium resize-none focus:border-blue-300 focus:outline-none transition-colors bg-gray-50 hover:bg-white"
                    value={about}
                    onChange={(e) => setAbout(e.target.value)}
                    placeholder="Tell us more about your organization, mission, values, and what makes you unique..."
                    rows={6}
                  />
                  <div className="absolute bottom-3 right-3 text-xs text-gray-400">
                    {about.length}/500
                  </div>
                </div>
              </div>
            </div>

            {/* Right: Top Announcements */}
            <div className="flex-[1] bg-white p-8 rounded-2xl shadow-lg border border-gray-100 h-fit hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-gray-800">
                  Latest Announcements
                </h4>
              </div>
              <div className="bg-gradient-to-br from-gray-50 to-blue-50 border border-gray-200 rounded-xl p-4">
                <SpotLights data={allAnnouncements} />
              </div>
              <button
              onClick={()=>router.push(`/business-dashboard/${orgProfile[0].businessType.businessName.replace(/\s+/g, '_')}-${orgProfile?.[0]?.userId}/announcements`)}
                className={`px-4 py-2 rounded-lg text-white text-sm font-medium transition-colors bg-blue-600 hover:bg-blue-700`}
              >view all</button>

            </div>
          </div>

          {/* Videos & Gallery Section */}
          <div className="space-y-8">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h4 className="text-2xl font-bold text-gray-800">
                Media & Gallery
              </h4>
            </div>

            <div className="flex flex-col lg:flex-row gap-8 w-full">
              {/* Social Media Links */}
              <div className="flex-[1] space-y-4">
                <h5 className="text-lg font-semibold text-gray-700 mb-4">Social Media</h5>
                <div className="space-y-3">
                  {socialMediaLinks.map((media) => (
                    <div
                      key={media.id}
                      className="group cursor-pointer flex items-center justify-between bg-white border-2 border-gray-200 px-6 py-4 rounded-xl shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-300"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 flex items-center justify-center rounded-xl bg-gray-50 border group-hover:bg-white transition-colors">
                          {media.icon}
                        </div>
                        <div>
                          <p className="font-medium text-gray-800">{media.name}</p>
                          <p className="text-sm text-gray-500 truncate max-w-[200px]">
                            {media.link || "Not connected"}
                          </p>
                        </div>
                      </div>
                      <div className="text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Upload Section */}
              <div className="flex-[2] bg-white border-2 border-gray-200 rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold text-gray-800">
                    Upload to Gallery
                  </h2>
                </div>

                <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 group">
                  <div className="space-y-4">
                    <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                      <svg className="w-8 h-8 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-lg font-medium text-gray-700 mb-2">
                        Drop your images here, or browse
                      </p>
                      <p className="text-sm text-gray-500">
                        Supports: JPG, PNG, GIF up to 10MB
                      </p>
                    </div>
                    <label className="cursor-pointer inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105">
                      Choose Files
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleFileChange}
                        multiple
                      />
                    </label>
                  </div>
                </div>

                {selectedFile && previewUrl && (
                  <div className="mt-6 p-4 border-2 border-green-200 rounded-xl bg-green-50">
                    <div className="flex items-start gap-4">
                      <Image
                        src={previewUrl}
                        alt="Selected"
                        width={80}
                        height={80}
                        className="rounded-lg object-cover border-2 border-white shadow-sm"
                      />
                      <div className="flex-1">
                        <p className="font-medium text-gray-800 mb-1">
                          {selectedFile.name}
                        </p>
                        <p className="text-sm text-gray-500 mb-2">
                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                        <div className="flex gap-2">
                          <button className="px-3 py-1 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors">
                            Upload
                          </button>
                          <button
                            onClick={() => {
                              setSelectedFile(null);
                              setPreviewUrl(null);
                            }}
                            className="px-3 py-1 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Location & Sports Configuration */}
          <div className="space-y-8">
            {/* Location Section */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-800">Location Settings</h3>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">State/Province</label>
                  <Select
                    name="selectedState"
                    options={allStatesList}
                    value={selectedState}
                    onChange={(selectedOption) => {
                      // Handle state selection
                    }}
                    isClearable
                    placeholder="Choose your state..."
                    className="w-full"
                    styles={{
                      control: (base) => ({
                        ...base,
                        borderRadius: '12px',
                        borderWidth: '2px',
                        borderColor: '#e5e7eb',
                        padding: '4px',
                        '&:hover': {
                          borderColor: '#3b82f6'
                        }
                      })
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Cities/Locations</label>
                  <Select
                    name="selectedLocations"
                    placeholder="Select cities you serve..."
                    options={allLocationsList}
                    isMulti
                    value={selectedLocations}
                    onChange={(selectedOption) => {
                      let selected =
                        (selectedOption as {
                          value: number;
                          label: string;
                        }[]) || [];

                      if (selected.some((city) => city.value === 0)) {
                        selected = [{ value: 0, label: "All Cities" }];
                      } else {
                        selected = selected.filter((city) => city.value !== 0);
                      }
                      // Handle location selection
                    }}
                    styles={{
                      control: (base) => ({
                        ...base,
                        borderRadius: '12px',
                        borderWidth: '2px',
                        borderColor: '#e5e7eb',
                        padding: '4px',
                        '&:hover': {
                          borderColor: '#3b82f6'
                        }
                      })
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Sports Section */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-800">Sports & Specialties</h3>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Primary Sport</label>
                  <Select
                    name="selectedSport"
                    options={allSportsList}
                    value={selectedSport}
                    onChange={(selectedOption) => {
                      // Handle sport selection
                    }}
                    isClearable
                    placeholder="Choose your primary sport..."
                    className="w-full"
                    styles={{
                      control: (base) => ({
                        ...base,
                        borderRadius: '12px',
                        borderWidth: '2px',
                        borderColor: '#e5e7eb',
                        padding: '4px',
                        '&:hover': {
                          borderColor: '#3b82f6'
                        }
                      })
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Specialties</label>
                  <Select
                    name="selectedSpecialities"
                    placeholder="Select your specialties..."
                    options={allSpecilitiesList}
                    isMulti
                    value={selectedSpecialities}
                    onChange={(selectedOption) => {
                      let selected =
                        (selectedOption as unknown as {
                          value: number;
                          label: string;
                        }[]) || [];

                      if (selected.some((item) => item.value === 0)) {
                        selected = [{ value: 0, label: "All Specialities" }];
                      } else {
                        selected = selected.filter((item) => item.value !== 0);
                      }
                      // Handle specialties selection
                    }}
                    styles={{
                      control: (base) => ({
                        ...base,
                        borderRadius: '12px',
                        borderWidth: '2px',
                        borderColor: '#e5e7eb',
                        padding: '4px',
                        '&:hover': {
                          borderColor: '#3b82f6'
                        }
                      })
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ClientGuard>
  );
};

export default BusinessProfile;
