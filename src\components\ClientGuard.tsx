// components/guards/ClientGuard.tsx
"use client"
import { AppDispatch } from "@/store"
import { logoutUser } from "@/store/slices/auth/loginSlice"
import { jwtDecode } from "jwt-decode"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { ReactNode, useEffect } from "react"
import { useDispatch } from "react-redux"

interface ClientGuardProps {
    allowedRoles: number[]
    children: ReactNode
}

export interface DecodedToken {
    exp: number;
    [key: string]: any;
}

export default function ClientGuard({ allowedRoles, children }: ClientGuardProps) {
    const { data: session, status } = useSession()
    const router = useRouter()
    const dispatch = useDispatch<AppDispatch>();


    const logoutAndClearStorage = () => {
        dispatch(logoutUser());
        localStorage.clear();
        sessionStorage.clear();
        router.push("/");
    };

    useEffect(() => {
        if (status === "loading") return;

        if (!session?.user) {
            router.push("/");
            return;
        }

        const decoded = jwtDecode<DecodedToken>(session?.user?.token);
        const currentTime = Date.now() / 1000;
        if (decoded.exp < currentTime) {
            logoutAndClearStorage();
        }

        const { roleId } = session?.user;
        if (!allowedRoles?.includes(roleId)) {
            router.push("/404");
        }
    }, [session, status, allowedRoles, router]);

    if (status === "loading") return null

    return <>{children}</>
}
