// announcementCard.tsx
import React, { useState } from "react";
import { Pencil, Trash2, Pin } from "lucide-react";
import clsx from "clsx";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { pinAnnouncement, deleteAnnouncement } from "@/store/slices/org-announcement/announcement";
import DeleteConfirmationModal from "@/components/modals/DeleteConfirmationModal";

type Announcement = {
  id: number;
  title: string;
  startDate: string;
  endDate: string;
  type: {
    id: number;
    typeName: string;
  };
  category: {
    id: number;
    categoryName: string;
  };
  isPinned: boolean;
  isActive: boolean;
};

interface Props {
  data: Announcement;
  onDelete?: (id: number) => void; // Made optional since we handle delete internally
}

const AnnouncementCard: React.FC<Props> = ({ data, onDelete }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { pinLoading, deleteLoading } = useSelector((state: RootState) => state.announcementDashboard);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const result = await dispatch(deleteAnnouncement(data.id));

      if (deleteAnnouncement.fulfilled.match(result)) {
        console.log(`✅ Announcement ${data.id} deleted successfully`);
        setShowDeleteModal(false);
        // Call the parent onDelete callback if needed for additional cleanup
        if (onDelete) {
          onDelete(data.id);
        }
      } else {
        console.error("❌ Failed to delete announcement");
      }
    } catch (error) {
      console.error("❌ Error deleting announcement:", error);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  const handlePinClick = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling

    try {
      const newPinStatus = data.isPinned ? 0 : 1; // Toggle pin status
      const result = await dispatch(pinAnnouncement({
        id: data.id,
        isPinned: newPinStatus
      }));

      if (pinAnnouncement.fulfilled.match(result)) {
        console.log(`✅ Announcement ${data.id} ${newPinStatus ? 'pinned' : 'unpinned'} successfully`);
        // You can add a toast notification here if you have a toast library
      } else {
        console.error("❌ Failed to toggle pin status");
        // You can add error toast here
      }
    } catch (error) {
      console.error("❌ Error toggling pin status:", error);
      // You can add error toast here
    }
  };

  return (
    <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
      {/* Mobile Layout (default) */}
      <div className="block lg:hidden">
        {/* Header with ID, Pin, and Actions */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <span className="text-xs font-bold text-gray-600 bg-white px-2 py-1 rounded-full">
              ID: {data.id}
            </span>
            <button
              onClick={handlePinClick}
              disabled={pinLoading}
              className={clsx(
                "p-1 rounded-full transition-all duration-200 hover:scale-110 relative",
                data.isPinned
                  ? "text-yellow-500 hover:bg-yellow-50"
                  : "text-gray-400 hover:text-yellow-500 hover:bg-yellow-50",
                pinLoading && "opacity-50 cursor-not-allowed"
              )}
              title={data.isPinned ? "Unpin announcement" : "Pin announcement"}
            >
              {pinLoading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Pin size={16} className={data.isPinned ? "animate-pulse" : ""} />
              )}
            </button>
          </div>

          <div className="flex items-center gap-2">
            <span
              className={clsx(
                "text-xs font-semibold px-2 py-1 rounded-full",
                data.isActive
                  ? "text-green-700 bg-green-100"
                  : "text-red-700 bg-red-100"
              )}
            >
              {data.isActive ? "Active" : "Inactive"}
            </span>
            <div className="flex gap-1">
              <button
                className="p-2 hover:bg-indigo-100 rounded-full transition-colors"
                title="Edit"
              >
                <Pencil className="text-indigo-600" size={14} />
              </button>
              <button
                className="p-2 hover:bg-red-100 rounded-full transition-colors"
                title="Delete"
                onClick={handleDeleteClick}
              >
                <Trash2 className="text-red-600" size={14} />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          <h2 className="text-lg font-bold text-gray-800 group-hover:text-indigo-700 transition-colors leading-tight">
            {data.title}
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">From</p>
              <p className="text-sm font-medium text-gray-800 mb-1">
                {new Date(data.startDate).toLocaleDateString()}
              </p>
              <p className="text-xs text-indigo-600 font-medium">
                {data.type?.typeName}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">To</p>
              <p className="text-sm font-medium text-gray-800 mb-1">
                {new Date(data.endDate).toLocaleDateString()}
              </p>
              <p className="text-xs text-indigo-600 font-medium">
                {data.category?.categoryName}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Layout (lg and above) */}
      <div className="hidden lg:block">
        <div className="grid grid-cols-12 items-center gap-6 p-6">
          {/* Left - ID + Pin */}
          <div className="col-span-2 flex flex-col items-start space-y-2">
            <span className="text-sm font-bold text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
              ID: {data.id}
            </span>
            <button
              onClick={handlePinClick}
              disabled={pinLoading}
              className={clsx(
                "p-2 rounded-full transition-all duration-200 hover:scale-110 relative",
                data.isPinned
                  ? "text-yellow-500 hover:bg-yellow-50"
                  : "text-gray-400 hover:text-yellow-500 hover:bg-yellow-50",
                pinLoading && "opacity-50 cursor-not-allowed"
              )}
              title={data.isPinned ? "Unpin announcement" : "Pin announcement"}
            >
              {pinLoading ? (
                <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Pin size={20} className={data.isPinned ? "animate-pulse" : ""} />
              )}
            </button>
          </div>

          {/* Center - Title + Dates */}
          <div className="col-span-7 text-center">
            <h2 className="text-xl font-bold text-gray-800 group-hover:text-indigo-700 transition-colors mb-4">
              {data.title}
            </h2>
            <div className="flex justify-center gap-12">
              <div className="text-left">
                <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-1">From</p>
                <p className="text-sm font-medium text-gray-800 mb-1">
                  {new Date(data.startDate).toLocaleDateString()}
                </p>
                <p className="text-sm text-indigo-600 font-medium">
                  {data.type?.typeName}
                </p>
              </div>
              <div className="text-left">
                <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-1">To</p>
                <p className="text-sm font-medium text-gray-800 mb-1">
                  {new Date(data.endDate).toLocaleDateString()}
                </p>
                <p className="text-sm text-indigo-600 font-medium">
                  {data.category?.categoryName}
                </p>
              </div>
            </div>
          </div>

          {/* Right - Status + Icons */}
          <div className="col-span-3 flex flex-col items-end space-y-4">
            <span
              className={clsx(
                "text-sm font-semibold px-3 py-1 rounded-full",
                data.isActive
                  ? "text-green-700 bg-green-100"
                  : "text-red-700 bg-red-100"
              )}
            >
              {data.isActive ? "Active" : "Inactive"}
            </span>
            <div className="flex gap-2">
              <button
                className="p-2 hover:bg-indigo-100 rounded-full transition-colors"
                title="Edit"
              >
                <Pencil className="text-indigo-600" size={18} />
              </button>
              <button
                className="p-2 hover:bg-red-100 rounded-full transition-colors"
                title="Delete"
                onClick={handleDeleteClick}
              >
                <Trash2 className="text-red-600" size={18} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Announcement"
        message="Are you sure you want to delete this announcement? This action cannot be undone and will permanently remove the announcement from your system."
        itemName={data.title}
        isLoading={deleteLoading}
      />
    </div>
  );
};

export default AnnouncementCard;
