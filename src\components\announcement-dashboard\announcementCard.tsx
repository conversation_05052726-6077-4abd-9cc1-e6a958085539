// announcementCard.tsx
import React from "react";
import { Pen<PERSON>l, Trash2, Pin } from "lucide-react";
import clsx from "clsx";

type Announcement = {
  id: number;
  title: string;
  startDate: string;
  endDate: string;
  type: {
    id: number;
    typeName: string;
  };
  category: {
    id: number;
    categoryName: string;
  };
  isPinned: boolean;
  isActive: boolean;
};

interface Props {
  data: Announcement;
  onDelete: (id: number) => void;
}

const AnnouncementCard: React.FC<Props> = ({ data, onDelete }) => {
  const handleDeleteClick = () => {
    console.log("AnnouncementCard data:", data?.id);
    if (window.confirm("Are you sure you want to delete this announcement?")) {
      onDelete(data?.id);
    }
  };

  return (
    <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
      {/* Mobile Layout (default) */}
      <div className="block lg:hidden">
        {/* Header with ID, Pin, and Actions */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <span className="text-xs font-bold text-gray-600 bg-white px-2 py-1 rounded-full">
              ID: {data.id}
            </span>
            {data.isPinned && (
              <div className="animate-pulse text-yellow-500" title="Pinned">
                <Pin size={16} />
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span
              className={clsx(
                "text-xs font-semibold px-2 py-1 rounded-full",
                data.isActive
                  ? "text-green-700 bg-green-100"
                  : "text-red-700 bg-red-100"
              )}
            >
              {data.isActive ? "Active" : "Inactive"}
            </span>
            <div className="flex gap-1">
              <button
                className="p-2 hover:bg-indigo-100 rounded-full transition-colors"
                title="Edit"
              >
                <Pencil className="text-indigo-600" size={14} />
              </button>
              <button
                className="p-2 hover:bg-red-100 rounded-full transition-colors"
                title="Delete"
                onClick={handleDeleteClick}
              >
                <Trash2 className="text-red-600" size={14} />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          <h2 className="text-lg font-bold text-gray-800 group-hover:text-indigo-700 transition-colors leading-tight">
            {data.title}
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">From</p>
              <p className="text-sm font-medium text-gray-800 mb-1">
                {new Date(data.startDate).toLocaleDateString()}
              </p>
              <p className="text-xs text-indigo-600 font-medium">
                {data.type?.typeName}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-1">To</p>
              <p className="text-sm font-medium text-gray-800 mb-1">
                {new Date(data.endDate).toLocaleDateString()}
              </p>
              <p className="text-xs text-indigo-600 font-medium">
                {data.category?.categoryName}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Layout (lg and above) */}
      <div className="hidden lg:block">
        <div className="grid grid-cols-12 items-center gap-6 p-6">
          {/* Left - ID + Pin */}
          <div className="col-span-2 flex flex-col items-start space-y-2">
            <span className="text-sm font-bold text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
              ID: {data.id}
            </span>
            {data.isPinned && (
              <div className="animate-pulse text-yellow-500" title="Pinned">
                <Pin size={20} />
              </div>
            )}
          </div>

          {/* Center - Title + Dates */}
          <div className="col-span-7 text-center">
            <h2 className="text-xl font-bold text-gray-800 group-hover:text-indigo-700 transition-colors mb-4">
              {data.title}
            </h2>
            <div className="flex justify-center gap-12">
              <div className="text-left">
                <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-1">From</p>
                <p className="text-sm font-medium text-gray-800 mb-1">
                  {new Date(data.startDate).toLocaleDateString()}
                </p>
                <p className="text-sm text-indigo-600 font-medium">
                  {data.type?.typeName}
                </p>
              </div>
              <div className="text-left">
                <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-1">To</p>
                <p className="text-sm font-medium text-gray-800 mb-1">
                  {new Date(data.endDate).toLocaleDateString()}
                </p>
                <p className="text-sm text-indigo-600 font-medium">
                  {data.category?.categoryName}
                </p>
              </div>
            </div>
          </div>

          {/* Right - Status + Icons */}
          <div className="col-span-3 flex flex-col items-end space-y-4">
            <span
              className={clsx(
                "text-sm font-semibold px-3 py-1 rounded-full",
                data.isActive
                  ? "text-green-700 bg-green-100"
                  : "text-red-700 bg-red-100"
              )}
            >
              {data.isActive ? "Active" : "Inactive"}
            </span>
            <div className="flex gap-2">
              <button
                className="p-2 hover:bg-indigo-100 rounded-full transition-colors"
                title="Edit"
              >
                <Pencil className="text-indigo-600" size={18} />
              </button>
              <button
                className="p-2 hover:bg-red-100 rounded-full transition-colors"
                title="Delete"
                onClick={handleDeleteClick}
              >
                <Trash2 className="text-red-600" size={18} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementCard;
