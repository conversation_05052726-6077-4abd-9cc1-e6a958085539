"use client";

import ClientGuard from "@/components/ClientGuard";
import FloatingMessagesWidget from "@/components/messages/FloatingMessagesWidget";
import MessagesScreen from "@/components/messages/MessagesScreen";
import { useRoleBasedRoute } from "@/hooks/useRoleBasedRouting";

const AthleteMessagesPage = () => {
  const { isAuthenticated } = useRoleBasedRoute();

  if (!isAuthenticated) return null;

  return (
    <ClientGuard allowedRoles={[2]}> {/* Athletes only */}
      <div className="h-[calc(100vh-5rem)] w-full">
        <MessagesScreen />
      </div>
    </ClientGuard>
  );
};

export default AthleteMessagesPage;
