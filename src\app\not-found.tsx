
const RouteNotFound = () => {
    return (
        <div className="flex flex-col items-center justify-center h-full px-4 text-center bg-white dark:bg-gray-900">
            <img
                src="/404.png"
                alt="Page not found"
                className="w-full max-w-md mb-8"
            />
            <h2 className="text-3xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                Page Not Found
            </h2>
            <p className="text-gray-500 dark:text-gray-400 mb-6 text-wrap text-center">
                The page you requested is not available. It may have been moved, had its name changed, or is temporarily inaccessible.
            </p>

            {/* <Button asChild variant="default" className="gap-2">
                <Link href="/">
                    <ArrowLeft className="h-4 w-4" />
                    Go Home
                </Link>
            </Button> */}
        </div>
    )
}
export default RouteNotFound