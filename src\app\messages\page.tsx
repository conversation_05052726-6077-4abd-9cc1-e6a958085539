"use client";

import ClientGuard from "@/components/ClientGuard";
import MessagesScreen from "@/components/messages/MessagesScreen";
import { useRoleBasedRoute } from "@/hooks/useRoleBasedRouting";

const MessagesPage = () => {
  const { isAuthenticated } = useRoleBasedRoute();

  if (!isAuthenticated) return null;

  return (
    <ClientGuard allowedRoles={[1, 2, 3, 4]}> {/* Athletes, Coaches, Business */}
      <div className="h-[calc(100vh-5rem)] w-full">
        <MessagesScreen />
      </div>
    </ClientGuard>
  );
};

export default MessagesPage;
