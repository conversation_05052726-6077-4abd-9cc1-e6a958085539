{"name": "connect-athlete", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@next/third-parties": "^15.3.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.3.0", "@stripe/react-stripe-js": "^3.0.0", "@stripe/stripe-js": "^5.2.0", "@tanstack/react-table": "^8.21.3", "@types/next-auth": "^3.15.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "formik": "^2.4.5", "framer-motion": "^11.6.0", "html-react-parser": "^5.1.10", "input-otp": "^1.4.1", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.445.0", "next": "14.2.13", "next-auth": "^4.24.7", "qrcode": "^1.5.4", "qrcode.react": "^4.1.0", "react": "^18", "react-datepicker": "^8.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-number-format": "^5.4.4", "react-redux": "^9.1.2", "react-select": "^5.8.0", "react-square-web-payments-sdk": "^3.2.1", "react-toastify": "^11.0.5", "socket.io": "^4.7.5", "socket.io-client": "^4.8.1", "square": "^36.0.0", "swiper": "^11.2.6", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-date-range": "^1.4.9", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18", "@types/react-redux": "^7.1.34", "@types/react-select": "^5.0.0", "eslint": "^8", "eslint-config-next": "14.2.13", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}