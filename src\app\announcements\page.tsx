// "use client";

// import React, { useEffect, useState } from "react";
// import { useDispatch, useSelector } from "react-redux";
// import { AppDispatch, RootState } from "@/store";
// import {
//   fetchAllAnnouncements,
//   fetchAnnouncementsCategory,
//   fetchAnnouncementType,
// } from "@/store/slices/org-announcement/announcement";
// import { Calendar, Pin } from "lucide-react";
// import CustomDropdown from "@/components/common/DropDown";

// const AnnouncementsPage = () => {
//   const [selectedType, setSelectedType] = useState<string>("");
//   const [selectedCategory, setSelectedCategory] = useState<string>("");
//   const [selectedStatus, setSelectedStatus] = useState<string>("Status");

//   const dispatch = useDispatch<AppDispatch>();
//   const {
//     allAnnouncements,
//     annoucementType,
//     orgAnnouncement,
//     loading,
//   } = useSelector((state: RootState) => state.announcementDashboard);

//   // Sample data for demonstration when API data is not available
//   const sampleAnnouncements = [
//     {
//       id: 1,
//       title: "Summer Training Camp Registration",
//       startDate: "2024-06-01",
//       endDate: "2024-08-31",
//       type: { typeName: "Registration" },
//       category: { categoryName: "Training" },
//       isPinned: true,
//     },
//     {
//       id: 2,
//       title: "Championship Tournament Announcement",
//       startDate: "2024-07-15",
//       endDate: "2024-07-30",
//       type: { typeName: "Event" },
//       category: { categoryName: "Competition" },
//       isPinned: false,
//     },
//     {
//       id: 3,
//       title: "New Facility Opening",
//       startDate: "2024-05-01",
//       endDate: "2024-12-31",
//       type: { typeName: "News" },
//       category: { categoryName: "Facility" },
//       isPinned: true,
//     },
//   ];

//   // Use sample data if API data is not available
//   const displayAnnouncements = allAnnouncements?.data?.length > 0
//     ? allAnnouncements.data
//     : sampleAnnouncements;

//   useEffect(() => {
//     dispatch(fetchAllAnnouncements());
//     dispatch(fetchAnnouncementType());
//     dispatch(fetchAnnouncementsCategory());
//   }, [dispatch]);

//   const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//     setSelectedType(e.target.value);
//   };

//   const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//     setSelectedCategory(e.target.value);
//   };

//   const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
//     setSelectedStatus(e.target.value);
//   };

//   const statusOptions = ["Status", "Active", "Inactive"];

//   return (
//     <div className="min-h-screen bg-gray-50 py-8 px-4">
//       <div className="max-w-6xl mx-auto">
//         {/* Header Section */}
//         <div className="text-center mb-8">
//           <h1 className="text-2xl font-bold text-blue-600 mb-2">
//             Sports Businesses - ANNOUNCEMENTS DASHBOARD
//           </h1>
//           <div className="text-blue-600 underline text-sm">
//             www.connectathlete/business/BusinessName-UserID/announcements
//           </div>
//         </div>

//         {/* Filter Section */}
//         <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
//           <div className="grid grid-cols-1 md:grid-cols-4 gap-6 items-end">
//             {/* Type Dropdown */}
//             <div className="space-y-2">
//               <label className="text-sm font-medium text-gray-700 block">Type</label>
//               <div className="bg-gray-200 rounded-md min-h-[40px]">
//                 <CustomDropdown
//                   options={annoucementType?.data?.map((item: any) => item?.typeName) || ["Drop down"]}
//                   selectedValue={selectedType || "Drop down"}
//                   handleChange={handleTypeChange}
//                   placeholder="Drop down"
//                 />
//               </div>
//             </div>

//             {/* Calendar Icon */}
//             <div className="flex justify-center items-end pb-2">
//               <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center border border-gray-300">
//                 <Calendar className="w-6 h-6 text-gray-600" />
//               </div>
//             </div>

//             {/* Category Dropdown */}
//             <div className="space-y-2">
//               <label className="text-sm font-medium text-gray-700 block">CATEGORY</label>
//               <div className="bg-gray-200 rounded-md min-h-[40px]">
//                 <CustomDropdown
//                   options={orgAnnouncement?.data?.map((item: any) => item?.categoryName) || ["Drop down"]}
//                   selectedValue={selectedCategory || "Drop down"}
//                   handleChange={handleCategoryChange}
//                   placeholder="Drop down"
//                 />
//               </div>
//             </div>

//             {/* Status */}
//             <div className="space-y-2">
//               <div className="flex items-center gap-2 mb-1">
//                 <div className="w-4 h-4 bg-green-500 rounded-sm"></div>
//                 <span className="text-sm font-medium text-gray-700">Status</span>
//               </div>
//               <div className="bg-gray-200 rounded-md min-h-[40px]">
//                 <CustomDropdown
//                   options={statusOptions}
//                   selectedValue={selectedStatus}
//                   handleChange={handleStatusChange}
//                   placeholder="Status"
//                 />
//               </div>
//             </div>
//           </div>
//         </div>

//         {/* Announcements List */}
//         <div className="space-y-6">
//           {loading ? (
//             <div className="text-center py-8">
//               <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
//               <p className="text-gray-600 mt-2">Loading announcements...</p>
//             </div>
//           ) : displayAnnouncements?.length > 0 ? (
//             displayAnnouncements.map((announcement: any, index: number) => (
//               <div
//                 key={announcement.id || index}
//                 className="bg-gray-300 rounded-lg p-8 shadow-sm border border-gray-400 hover:shadow-md transition-shadow"
//               >
//                 <div className="grid grid-cols-12 items-center gap-4">
//                   {/* Left Section - ID and Pin */}
//                   <div className="col-span-2 flex flex-col items-start space-y-2">
//                     <div className="text-sm font-bold text-gray-800">
//                       ID
//                     </div>
//                     {announcement.isPinned && (
//                       <Pin className="w-6 h-6 text-gray-700 transform rotate-45" />
//                     )}
//                   </div>

//                   {/* Center Section - Title and Details */}
//                   <div className="col-span-8 text-center">
//                     <h3 className="text-xl font-bold text-gray-900 mb-6">
//                       {announcement.title || "Announcement Title"}
//                     </h3>

//                     <div className="flex justify-center items-start gap-20">
//                       <div className="text-left space-y-1">
//                         <div className="text-sm font-semibold text-gray-800">From :</div>
//                         <div className="text-sm text-gray-700 font-medium">
//                           {announcement.startDate
//                             ? new Date(announcement.startDate).toLocaleDateString()
//                             : "Start Date"
//                           }
//                         </div>
//                         <div className="text-sm text-gray-700 italic">
//                           &lt;{announcement.type?.typeName || "Type"}&gt;
//                         </div>
//                       </div>

//                       <div className="text-left space-y-1">
//                         <div className="text-sm font-semibold text-gray-800">To:</div>
//                         <div className="text-sm text-gray-700 font-medium">
//                           {announcement.endDate
//                             ? new Date(announcement.endDate).toLocaleDateString()
//                             : "End Date"
//                           }
//                         </div>
//                         <div className="text-sm text-gray-700 italic">
//                           &lt;{announcement.category?.categoryName || "CATEGORY Value"}&gt;
//                         </div>
//                       </div>
//                     </div>
//                   </div>

//                   {/* Right Section - Empty for layout balance */}
//                   <div className="col-span-2"></div>
//                 </div>
//               </div>
//             ))
//           ) : (
//             <div className="text-center py-12">
//               <div className="bg-gray-300 rounded-lg p-8 shadow-sm border border-gray-400">
//                 <div className="text-gray-600 text-lg mb-2">No announcements found</div>
//                 <p className="text-gray-500">Create your first announcement to get started</p>
//               </div>
//             </div>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default AnnouncementsPage;
