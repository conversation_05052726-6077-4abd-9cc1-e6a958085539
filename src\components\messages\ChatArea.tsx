"use client";

import { MoreVertical, Phone, Send, Video, Paperclip, X, FileText, Image as ImageIcon, File, Wifi, WifiOff } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import Avatar from "../common/Avatar";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { ChatMessage, Conversation } from "./MessagesScreen";
import { useSocket } from "@/contexts/SocketContext";
import { toast } from "react-toastify";

interface ChatAreaProps {
  conversation: Conversation;
  messages: ChatMessage[];
  onSendMessage: (content: string, attachment?: FileAttachment) => void;
}

interface FileAttachment {
  file: File;
  type: 'image' | 'document' | 'pdf';
  url: string;
}

const ChatArea = ({ conversation, messages, onSendMessage }: ChatAreaProps) => {
  const [newMessage, setNewMessage] = useState("");
  const [selectedFile, setSelectedFile] = useState<FileAttachment | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Socket integration
  const { isConnected, sendMessage: sendSocketMessage, joinConversation, leaveConversation } = useSocket();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Join conversation when component mounts or conversation changes
  useEffect(() => {
    if (conversation?.id) {
      joinConversation(conversation.id.toString());

      // Cleanup: leave conversation when component unmounts or conversation changes
      return () => {
        leaveConversation(conversation.id.toString());
      };
    }
  }, [conversation?.id, joinConversation, leaveConversation]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() && !selectedFile) return;
    if (isSending) return; // Prevent double sending

    setIsSending(true);

    try {
      // Prepare attachment data if exists
      const attachmentData = selectedFile ? {
        file: selectedFile.file,
        type: selectedFile.type,
        url: selectedFile.url
      } : undefined;

      // Send message through socket
      const success = await sendSocketMessage(
        conversation.id.toString(),
        newMessage.trim() || "📎 Attachment",
        attachmentData
      );

      if (success) {
        // Also call the original onSendMessage for local state update
        onSendMessage(newMessage.trim() || "📎 Attachment", selectedFile || undefined);

        // Clear input fields
        setNewMessage("");
        setSelectedFile(null);

        toast.success("Message sent successfully!");
      } else {
        // Fallback to original method if socket fails
        onSendMessage(newMessage.trim() || "📎 Attachment", selectedFile || undefined);
        setNewMessage("");
        setSelectedFile(null);

        toast.warning("Message sent locally (socket connection issue)");
      }
    } catch (error) {
      console.error("Error sending message:", error);

      // Fallback to original method
      onSendMessage(newMessage.trim() || "📎 Attachment", selectedFile || undefined);
      setNewMessage("");
      setSelectedFile(null);

      toast.error("Failed to send message through socket, sent locally");
    } finally {
      setIsSending(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type restrictions
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain', 'text/csv'
    ];

    // Restrict video files
    if (file.type.startsWith('video/') || file.name.toLowerCase().endsWith('.mp4')) {
      alert('Video files are not allowed. Please select images, PDFs, or documents.');
      return;
    }

    if (!allowedTypes.includes(file.type)) {
      alert('File type not supported. Please select images, PDFs, or documents.');
      return;
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB.');
      return;
    }

    const fileUrl = URL.createObjectURL(file);
    let fileType: 'image' | 'document' | 'pdf' = 'document';

    if (file.type.startsWith('image/')) {
      fileType = 'image';
    } else if (file.type === 'application/pdf') {
      fileType = 'pdf';
    }

    setSelectedFile({
      file,
      type: fileType,
      url: fileUrl
    });

    // Clear the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeSelectedFile = () => {
    if (selectedFile) {
      URL.revokeObjectURL(selectedFile.url);
      setSelectedFile(null);
    }
  };

  const getFileIcon = (type: string) => {
    if (type === 'image') return <ImageIcon className="w-4 h-4" />;
    if (type === 'pdf') return <FileText className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  // Function to detect and render links in text
  const renderMessageWithLinks = (text: string) => {
    // URL regex pattern
    const urlRegex = /(https?:\/\/[^\s]+|www\.[^\s]+|[^\s]+\.[a-z]{2,}(?:\/[^\s]*)?)/gi;

    const parts = text.split(urlRegex);

    return parts.map((part, index) => {
      if (urlRegex.test(part)) {
        // Ensure the URL has a protocol
        let url = part;
        if (!part.startsWith('http://') && !part.startsWith('https://')) {
          url = 'https://' + part;
        }

        return (
          <a
            key={index}
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="underline hover:no-underline font-medium"
            onClick={(e) => e.stopPropagation()}
          >
            {part}
          </a>
        );
      }
      return part;
    });
  };

  const formatMessageTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const formatMessageDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  };

  const shouldShowDateSeparator = (currentMessage: ChatMessage, previousMessage?: ChatMessage) => {
    if (!previousMessage) return true;

    const currentDate = new Date(currentMessage.timestamp).toDateString();
    const previousDate = new Date(previousMessage.timestamp).toDateString();

    return currentDate !== previousDate;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar
                profileImg={conversation?.galleries[0]?.fileLocation}
                name={conversation.firstName.charAt(0)}
                styles="h-10 w-10 bg-slate-700 text-white"
              />
              {conversation.isOnline && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
              )}
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {conversation.firstName} {conversation.lastName}
              </h2>
              <p className="text-sm text-gray-500">
                {conversation.isOnline ? (
                  <span className="text-green-600">Online</span>
                ) : conversation.lastSeen ? (
                  `Last seen ${formatMessageTime(conversation.lastSeen)}`
                ) : (
                  conversation.email
                )}
              </p>
            </div>
          </div>

          {/* Socket Connection Status */}
          <div className="flex items-center space-x-2">
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
              isConnected
                ? 'bg-green-100 text-green-700'
                : 'bg-red-100 text-red-700'
            }`}>
              {isConnected ? (
                <>
                  <Wifi className="w-3 h-3" />
                  <span>Connected</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-3 h-3" />
                  <span>Offline</span>
                </>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          {/* <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon" className="text-gray-500 hover:text-gray-700">
              <Phone className="w-5 h-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-500 hover:text-gray-700">
              <Video className="w-5 h-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-500 hover:text-gray-700">
              <MoreVertical className="w-5 h-5" />
            </Button>
          </div> */}
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
        <div className="space-y-4">
          {messages.map((message, index) => {
            const previousMessage = index > 0 ? messages[index - 1] : undefined;
            const showDateSeparator = shouldShowDateSeparator(message, previousMessage);
            const isOwnMessage = message.senderName === "You";

            return (
              <div key={message.id}>
                {/* Date Separator */}
                {showDateSeparator && (
                  <div className="flex justify-center my-4">
                    <span className="px-3 py-1 text-xs text-gray-500 bg-white rounded-full border">
                      {formatMessageDate(message.timestamp)}
                    </span>
                  </div>
                )}

                {/* Message */}
                <div className={`flex ${isOwnMessage ? "justify-end" : "justify-start"}`}>
                  <div className={`flex items-end space-x-2 max-w-xs lg:max-w-md ${isOwnMessage ? "flex-row-reverse space-x-reverse" : ""}`}>
                    {!isOwnMessage ? (
                      <Avatar
                        profileImg={conversation?.galleries[0]?.fileLocation}
                        name={conversation.firstName.charAt(0)}
                        styles="h-8 w-8 bg-slate-700 text-white flex-shrink-0"
                      />
                    ) : (
                      <Avatar
                        profileImg=""
                        name="Y"
                        styles="h-8 w-8 bg-blue-600 text-white flex-shrink-0"
                      />
                    )}
                    <div
                      className={`px-4 py-2 rounded-2xl ${isOwnMessage
                          ? "bg-blue-600 text-white rounded-br-md"
                          : "bg-white text-gray-900 border border-gray-200 rounded-bl-md"
                        }`}
                    >
                      {/* Message Content */}
                      {message.content && message.content !== "📎 Attachment" && (
                        <p className="text-sm">{renderMessageWithLinks(message.content)}</p>
                      )}

                      {/* Attachment */}
                      {message.attachment && (
                        <div className="mt-2">
                          {message.attachment.type === 'image' ? (
                            <div>
                              <img
                                src={message.attachment.url}
                                alt={message.attachment.name}
                                className="max-w-full max-h-48 rounded border cursor-pointer"
                                onClick={() => window.open(message.attachment?.url, '_blank')}
                              />
                              <p className={`text-xs mt-1 ${isOwnMessage ? "text-blue-100" : "text-gray-500"}`}>
                                {message.attachment.name}
                              </p>
                            </div>
                          ) : (
                            <div className={`flex items-center space-x-2 p-2 rounded border ${
                              isOwnMessage ? "border-blue-400 bg-blue-500" : "border-gray-300 bg-gray-50"
                            }`}>
                              {getFileIcon(message.attachment.type)}
                              <div className="flex-1 min-w-0">
                                <p className={`text-xs font-medium truncate ${
                                  isOwnMessage ? "text-white" : "text-gray-900"
                                }`}>
                                  {message.attachment.name}
                                </p>
                                <p className={`text-xs ${
                                  isOwnMessage ? "text-blue-100" : "text-gray-500"
                                }`}>
                                  {(message.attachment.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                className={`h-6 w-6 ${
                                  isOwnMessage
                                    ? "text-white hover:bg-blue-500"
                                    : "text-gray-500 hover:bg-gray-200"
                                }`}
                                onClick={() => {
                                  const link = document.createElement('a');
                                  link.href = message.attachment?.url || '';
                                  link.download = message.attachment?.name || 'file';
                                  link.click();
                                }}
                              >
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </Button>
                            </div>
                          )}
                        </div>
                      )}

                      <p
                        className={`text-xs mt-1 ${isOwnMessage ? "text-blue-100" : "text-gray-500"
                          }`}
                      >
                        {formatMessageTime(message.timestamp)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 bg-white">
        {/* File Preview */}
        {selectedFile && (
          <div className="mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getFileIcon(selectedFile.type)}
                <div>
                  <p className="text-sm font-medium text-gray-900">{selectedFile.file.name}</p>
                  <p className="text-xs text-gray-500">
                    {(selectedFile.file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={removeSelectedFile}
                className="h-8 w-8 text-gray-500 hover:text-gray-700"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            {selectedFile.type === 'image' && (
              <div className="mt-2">
                <img
                  src={selectedFile.url}
                  alt="Preview"
                  className="max-w-full h-20 object-cover rounded border"
                />
              </div>
            )}
          </div>
        )}

        <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileSelect}
            className="hidden"
            accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv"
          />
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => fileInputRef.current?.click()}
            className="text-gray-500 hover:text-gray-700"
            title="Attach file"
          >
            <Paperclip className="w-4 h-4" />
          </Button>
          <Input
            type="text"
            placeholder={`Message ${conversation.firstName}...`}
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            className="flex-1 bg-gray-50 border-gray-200 focus:bg-white"
          />
          <Button
            type="submit"
            size="icon"
            disabled={(!newMessage.trim() && !selectedFile) || isSending}
            className={`text-white ${
              isConnected
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-gray-500 hover:bg-gray-600'
            }`}
            title={isConnected ? 'Send message' : 'Offline - message will be sent locally'}
          >
            {isSending ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default ChatArea;
