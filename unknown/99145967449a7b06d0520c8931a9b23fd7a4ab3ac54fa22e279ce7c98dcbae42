'use client'
import UploadFiles from "@/components/common/UploadFiles"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { useDispatch, useSelector } from "react-redux"

const CoachVideo = () => {
    const { toggleAboutVideo, aboutVideoFile } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleToggleSection = () => {
        dispatch(handleCoachInputChange({ name: 'toggleAboutVideo', value: !toggleAboutVideo }))
    }

    const handleChangeVideoFile = (name, file) => {
        if (name === 'add') {
            dispatch(handleCoachInputChange({ name: 'aboutVideoFile', value: file }))
        } else if (name === 'remove') {
            dispatch(handleCoachInputChange({ name: 'aboutVideoFile', value: null }))
        }
    }

    return (
        <>
            <div className="flex flex-col gap-5 bg-slate-100 rounded-lg p-4">
                <div className="flex items-center justify-center gap-5">
                    <h2 className="text-primary text-lg font-bold">About John</h2>
                    <Switch checked={toggleAboutVideo} onCheckedChange={handleToggleSection} />
                </div>
                {toggleAboutVideo ?
                    <UploadFiles
                        acceptType={["video/mp4"]}
                        value={aboutVideoFile}
                        onFileSelect={(file) => handleChangeVideoFile('add', file)}
                        handleRemove={() => handleChangeVideoFile('remove', null)}
                        className="w-full"
                    /> :
                    null
                }

                {aboutVideoFile && <div className="flex justify-end">
                    <Button>Save</Button>
                </div>}
            </div>
        </>
    )
}
export default CoachVideo