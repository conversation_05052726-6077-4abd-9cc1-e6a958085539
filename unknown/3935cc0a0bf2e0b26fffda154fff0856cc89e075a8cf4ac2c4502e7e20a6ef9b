import { CoachSportStates } from "@/utils/interfaces";
import { createSlice } from "@reduxjs/toolkit";

export const emptyStatsFormData = {
  statsAsOf: undefined,
  yearsCoaching: "",
  athletesTrained: "",
  toggleAthletesTrained: true,
  teamChampionshipWon: "",
  toggleChampionshipWon: true,
  athleteImprovementRate: "",
  toggleAthleteImprovementRate: true,
  athleteRetentionRate: "",
  toggleRetentionRate: true,
  certificationsEarned: "",
  toggleCertificationEarned: true,
  description: "",
};

export const emptyAthletePlacementsList = [
  {
    id: "athletesPlacedTotal",
    placement: "Athletes Placed Total",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedInNCAAD1",
    placement: "Athletes Placed In NCAA D1",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedInNCAAD2",
    placement: "Athletes Placed In NCAA D2",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedInNCAAD3",
    placement: "Athletes Placed In NCAA D3",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedInNAIA",
    placement: "Athletes Placed In NAIA",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedInJUCO",
    placement: "Athletes Placed In JUCO",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedInPrepSchool",
    placement: "Athletes Placed In Prep School",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedInternationally",
    placement: "Athletes Placed Internationally",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedProfessional",
    placement: "Athletes Turned Professional",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedSemiPro",
    placement: "Athletes Placed In Semi-Pro",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedCombines",
    placement: "Athletes Placed In Showcases/Combines",
    athletesTrained: "",
    toggleHide: false,
  },
  {
    id: "athletesPlacedUnsigned",
    placement: "Athletes Placed Unsigned commits/Uncommitted ",
    athletesTrained: "",
    toggleHide: false,
  },
];

const initialState: CoachSportStates = {
  loading: false,
  error: "",
  selectedSport: null,
  togglePrimary: true,
  addedSportLevelsList: [],
  addedSpecilitiesList: [],
  toggleTeamCoaching: true,
  currentTeamList: [
    { id: 1, team: "", isPrimary: true },
    { id: 2, team: "", isPrimary: false },
    { id: 3, team: "", isPrimary: false },
  ],
  toggleStrengths: true,
  addedUniqueStrengths: [],
  toggleStatsForm: true,
  statsFormData: emptyStatsFormData,
  athletePlacementsList: emptyAthletePlacementsList,
  scoreCardDate: undefined,
  addedStatsScoreCardList: [],
  scoreCardId: null,
  toggleTrackRecord: true,
  isAddTrackRecord: false,
  trackRecordItem: null,
  addedTrackRecordsList: [],
  toggleVideoSection: true,
  highLightVideoData: null,
  addedHighLightVideosList: [],
  toggleHighLightLinks: true,
  isEditHighlightLinks: false,
  highLightLinksList: Array(6).fill({
    text: "",
    url: "",
  }),
  imagesGalleryList: [],
};

const coachSportSlice = createSlice({
  name: "coachSportProfile",
  initialState,
  reducers: {
    handleCoachSportInputChange: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
    updateTeamData: (state, action) => {
      const { id, field, value } = action.payload;
      if (field === "isPrimary") {
        state.currentTeamList = state.currentTeamList.map((team) => ({
          ...team,
          isPrimary: team.id === id,
        }));
      } else {
        const team = state.currentTeamList.find((team) => team.id === id);
        if (team) team[field] = value as string;
      }
    },
    updateAthletePlacement: (state, action) => {
      const { id, key, value } = action.payload;
      const index = state.athletePlacementsList.findIndex(
        (item) => item.id === id
      );
      if (index !== -1) {
        state.athletePlacementsList[index][key] = value;
      }
    },
  },
});

export const {
  handleCoachSportInputChange,
  updateTeamData,
  updateAthletePlacement,
} = coachSportSlice.actions;
export default coachSportSlice.reducer;
