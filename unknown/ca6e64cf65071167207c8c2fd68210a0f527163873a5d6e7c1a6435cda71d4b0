'use client'

import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>Provider,
    <PERSON><PERSON><PERSON>Trigger,
} from "@/components/ui/tooltip"
import { ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "../ui/button"

const BackButton = () => {
    const router = useRouter()

    const handleBack = () => {
        router.back()
    }

    return (
        <>
            <div>
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button className="self-start text-start border-slate-500 rounded-3xl" size={'icon'} variant={'outline'} onClick={handleBack}>
                                <ArrowLeft />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Back</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </>
    )
}
export default BackButton