import VerificationForm from "@/components/common/VerificationForm"
import { RootState } from "@/store"
import { EachSearchItem } from "@/utils/interfaces"
import { useDispatch, useSelector } from "react-redux"

const VerificationStepTwo = () => {
    const { additionalDocList } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: string | File | null | EachSearchItem) => {

    }

    return (
        <>
            <div className="flex flex-col gap-7">
                {additionalDocList?.map(item =>
                    <VerificationForm
                        data={item}
                        handleOnChange={handleOnChange}
                        step={2}
                    />
                )}
            </div>
        </>
    )
}
export default VerificationStepTwo