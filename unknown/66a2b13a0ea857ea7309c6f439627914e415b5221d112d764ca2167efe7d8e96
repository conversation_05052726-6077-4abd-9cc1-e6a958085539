import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { ContactInfo } from "@/utils/interfaces"
import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import { PencilLine } from "lucide-react"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"

const coachProfileSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().min(1, "Email is required").email("Invalid email format"),
    phone: z.string().optional(),
});


type FormData = z.infer<typeof coachProfileSchema>;

const CoachContactInfo = () => {
    const { toggleContactInfo, coachContactInfo, isEditContactInfo, toggleEmail, togglePhone } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: boolean | string | ContactInfo) => {
        dispatch(handleCoachInputChange({ name, value }))
    }

    const {
        register,
        control,
        handleSubmit,
        setValue,
        formState: { errors },
        watch,
        reset,
    } = useForm<FormData>({
        resolver: zodResolver(coachProfileSchema),
        defaultValues: {
            firstName: coachContactInfo?.firstName || '',
            lastName: coachContactInfo?.lastName || '',
            email: coachContactInfo?.email || '',
            phone: coachContactInfo?.phone || ''
        }
    });

    const handleCancel = () => {
        dispatch(handleCoachInputChange({ name: 'isEditContactInfo', value: !isEditContactInfo }))
        reset()
    }

    const handleEdit = () => {
        dispatch(handleCoachInputChange({ name: 'isEditContactInfo', value: true }));

        // Set form values manually when edit mode is triggered
        setValue('firstName', coachContactInfo?.firstName || '');
        setValue('lastName', coachContactInfo?.lastName || '');
        setValue('email', coachContactInfo?.email || '');
        setValue('phone', coachContactInfo?.phone || '');
    };


    useEffect(() => {
        if (isEditContactInfo) {
            reset({
                firstName: coachContactInfo?.firstName || '',
                lastName: coachContactInfo?.lastName || '',
                email: coachContactInfo?.email || '',
                phone: coachContactInfo?.phone || ''
            });
        }
    }, [isEditContactInfo, coachContactInfo, reset]);

    const onSubmit = (data: FormData) => {
        console.log("Submit Data:", data);
        // dispatch API call here
        dispatch(handleCoachInputChange({ name: 'coachContactInfo', value: data }))
        handleCancel()
    };


    return (
        <>
            <div className='flex flex-col rounded-lg p-4 bg-slate-100'>
                <div className="flex items-center justify-center gap-5">
                    <h2 className="text-xl font-bold">Contact Info</h2>
                    <Switch
                        checked={toggleContactInfo}
                        onCheckedChange={(checked) => handleOnChange('toggleContactInfo', checked)} />
                </div>

                <div className="flex justify-end">
                    <Button size={'icon'} variant={'outline'}
                        onClick={handleEdit}>
                        <PencilLine />
                    </Button>
                </div>

                {toggleContactInfo &&
                    <>
                        <div className="bg-slate-100 rounded-lg p-5 flex flex-col gap-5">
                            {isEditContactInfo ? (
                                <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-10">
                                    <div className="grid grid-cols-1 md:grid-cols-2 justify-center gap-6">
                                        <div className="flex flex-col gap-1">
                                            <Label>First Name</Label>
                                            <Input {...register("firstName")} placeholder="First Name" />
                                            {errors.firstName && (
                                                <p className="text-sm text-red-500">{errors.firstName.message}</p>
                                            )}
                                        </div>
                                        <div className="flex flex-col gap-1">
                                            <Label>Last Name</Label>
                                            <Input {...register("lastName")} placeholder="Last Name" />
                                            {errors.lastName && (
                                                <p className="text-sm text-red-500">{errors.lastName.message}</p>
                                            )}
                                        </div>
                                        <div className="flex flex-col gap-1">
                                            <div className="flex items-center gap-3">
                                                <Label>Email</Label>
                                                <Switch checked={toggleEmail}
                                                    onCheckedChange={(checked) => handleOnChange('toggleEmail', checked)} />
                                            </div>
                                            {toggleEmail && <Input {...register("email")} placeholder="Email" />}
                                            {errors.email && (
                                                <p className="text-sm text-red-500">{errors.email.message}</p>
                                            )}
                                            <div className="flex items-center gap-2 mt-3">
                                                <Button className="bg-blue-600 text-white" type="button">Verify My Email</Button>
                                                <Button variant={'link'} className="underline" type="button">Resend Link</Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col gap-1">
                                            <div className="flex items-center gap-3">
                                                <Label>Phone</Label>
                                                <Switch checked={togglePhone}
                                                    onCheckedChange={(checked) => handleOnChange('togglePhone', checked)} />
                                            </div>
                                            {togglePhone && <Input {...register("phone")} placeholder="Phone" />}
                                        </div>
                                    </div>
                                    <div className="flex justify-end gap-3">
                                        <Button className="w-24" variant={'outline'}
                                            onClick={handleCancel}>Cancel</Button>
                                        <Button className="w-24" type="submit">Save</Button>
                                    </div>
                                </form>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 items-center gap-8">
                                    <div className="flex flex-col gap-1">
                                        <Label>First Name</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachContactInfo?.firstName}
                                        </p>
                                    </div>
                                    <div className="flex flex-col gap-1">
                                        <Label>Last Name</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachContactInfo?.lastName}
                                        </p>
                                    </div>
                                    {coachContactInfo?.phone && togglePhone && <div className="flex flex-col gap-1">
                                        <Label>Phone</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachContactInfo?.phone}
                                        </p>
                                    </div>}
                                    {toggleEmail && <div className="flex flex-col gap-1">
                                        <Label>Email</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachContactInfo?.email}
                                        </p>
                                    </div>}
                                    <div className="flex flex-col gap-1">
                                        <Label>Latest T&C Accepted Date</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {format(new Date(), 'MMM, dd yyyy hh:mm:ss')}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </>
                }
            </div >
        </>
    )
}
export default CoachContactInfo