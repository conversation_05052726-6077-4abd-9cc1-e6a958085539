import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import GovtIdStepOne from "./GovtIdStepOne"
import VerificationStepTwo from "./VerificationStepTwo"

const CoachVerification = () => {
    return (
        <>
            <div className="flex flex-col bg-slate-100 rounded-lg p-5 gap-10">
                <h3 className="text-xl font-bold text-center">Coach Verification</h3>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 1:  {" "}
                        <span className="text-secondary">Please Upload one Goverment ID</span>
                    </h2>
                    <GovtIdStepOne />
                </div>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 2:  {" "}
                        <span className="text-secondary"> Pleae Upload atleast one Additional Document</span>
                    </h2>
                    <VerificationStepTwo />
                </div>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 3:  {" "}
                        <span className="text-secondary">Please check these these boxes and then click
                            before uploading these documents</span>
                    </h2>

                    <div className="flex flex-col p-4 gap-8 rounded-xl">
                        <div className="flex items-center gap-2">
                            <Checkbox className="border-slate-500" />
                            <Label>My name is clearly visible on this document.</Label>
                        </div>
                        <div className="flex items-center gap-2">
                            <Checkbox className="border-slate-500" />
                            <Label>The certification is still valid.</Label>
                        </div>
                        <div className="flex items-center gap-2">
                            <Checkbox className="border-slate-500" />
                            <Label>This was issued by a recognized authority.</Label>
                        </div>
                    </div>
                </div>

                <div className="flex items-center justify-center">
                    <Button>
                        Confirm & Save
                    </Button>
                </div>
            </div>
        </>
    )
}
export default CoachVerification