"use client";
import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import socketService from '@/services/socketService';
import type { IncomingMessage } from '@/services/socketService';

interface SocketContextType {
  isConnected: boolean;
  sendMessage: (receiverId: string, content: string, attachment?: any) => Promise<boolean>;
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  onMessage: (handler: (msg: IncomingMessage) => void) => () => void;
  sendTyping: (receiverId: string, isTyping: boolean) => void;
  reconnect: () => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isConnected, setIsConnected] = useState<boolean>(socketService.getConnectionStatus());

  useEffect(() => {
    const unsub = socketService.onConnectionChange(setIsConnected);
    socketService.reconnect();
    return () => {
      unsub();
      socketService.disconnect();
    };
  }, []);

  const sendMessage = useCallback(async (receiverId: string, content: string, attachment?: any) => {
    const senderId = sessionStorage.getItem('userId')!;
    return socketService.sendMessage({ senderId, receiverId, content, timestamp: new Date(), attachment });
  }, []);

  const context: SocketContextType = {
    isConnected,
    sendMessage,
    joinConversation: socketService.joinConversation.bind(socketService),
    leaveConversation: socketService.leaveConversation.bind(socketService),
    onMessage: socketService.onMessage.bind(socketService),
    sendTyping: socketService.sendTyping.bind(socketService),
    reconnect: socketService.reconnect.bind(socketService),
  };

  return <SocketContext.Provider value={context}>{children}</SocketContext.Provider>;
};

export function useSocket() {
  const ctx = useContext(SocketContext);
  if (!ctx) throw new Error('useSocket must be inside SocketProvider');
  return ctx;
}
