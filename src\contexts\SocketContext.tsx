"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import socketService from '@/services/socketService';
import type { IncomingMessage } from '@/services/socketService';

interface SocketContextType {
  isConnected: boolean;
  sendMessage: (receiverId: string, content: string, attachment?: any) => Promise<boolean>;
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  onMessage: (handler: (message: IncomingMessage) => void) => () => void;
  sendTyping: (receiverId: string, isTyping: boolean) => void;
  reconnect: () => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Subscribe to connection status changes
    const unsubscribe = socketService.onConnectionChange((connected) => {
      setIsConnected(connected);
    });

    // Set initial connection status
    setIsConnected(socketService.getConnectionStatus());

    // Cleanup on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  const sendMessage = async (
    receiverId: string, 
    content: string, 
    attachment?: {
      file: File;
      type: 'image' | 'document' | 'pdf';
      url: string;
    }
  ): Promise<boolean> => {
    const userId = sessionStorage.getItem("userId");
    if (!userId) {
      console.error('User ID not found');
      return false;
    }

    const messageData = {
      senderId: userId,
      receiverId,
      content,
      timestamp: new Date(),
      attachment: attachment ? {
        file: attachment.file,
        type: attachment.type,
        url: attachment.url,
        name: attachment.file.name,
        size: attachment.file.size,
      } : undefined,
    };

    return await socketService.sendMessage(messageData);
  };

  const joinConversation = (conversationId: string) => {
    socketService.joinConversation(conversationId);
  };

  const leaveConversation = (conversationId: string) => {
    socketService.leaveConversation(conversationId);
  };

  const onMessage = (handler: (message: IncomingMessage) => void) => {
    return socketService.onMessage(handler);
  };

  const sendTyping = (receiverId: string, isTyping: boolean) => {
    socketService.sendTyping(receiverId, isTyping);
  };

  const reconnect = () => {
    socketService.reconnect();
  };

  const contextValue: SocketContextType = {
    isConnected,
    sendMessage,
    joinConversation,
    leaveConversation,
    onMessage,
    sendTyping,
    reconnect,
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

// Custom hook to use socket context
export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export default SocketContext;
