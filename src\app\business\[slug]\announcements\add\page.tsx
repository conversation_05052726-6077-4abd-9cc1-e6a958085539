"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchAllStates, fetchAllLocations } from "@/store/slices/commonSlice";
import {
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
  createAnnouncement
} from "@/store/slices/org-announcement/announcement";
import { useSnackbar } from "@/contexts/SnackbarContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Upload, Calendar, MapPin, Phone, Mail, Link, ArrowLeft } from "lucide-react";
import { BusinessRoutes } from "@/utils/businessRoutes";
import DateRangePicker from "@/components/common/DatePicker";
import { channelsList } from "@/utils/constantDropdown";

interface AnnouncementFormData {
  title: string;
  details: string;
  image: File | null;
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  inspectionDate: string;
  area: string;
  type: string;
  category: string;
  channel: string;
  state: string;
  city: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  quickLinks: string[];
  isPublished: boolean;
}

const AddAnnouncementPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const { showSuccess, showError } = useSnackbar();

  // Redux state
  const {
    annoucementType,
    orgAnnouncement: announcementCategories,
    loading,
  } = useSelector((state: RootState) => state.announcementDashboard);

  // Common slice state for states and locations
  const {
    allStatesList,
    allLocationsList,
  } = useSelector((state: RootState) => state.commonSlice);

    const { orgProfile, selectedState, selectedLocations, selectedSport, selectedSpecialities } = useSelector((state: RootState) => state.orgProfile);
  
  console.log("orgProfile", orgProfile[0]?.id,  orgProfile[0]?.userId);

  // Form state
  const [formData, setFormData] = useState<AnnouncementFormData>({
    title: "",
    details: "",
    image: null,
    dateRange: {
      start: null,
      end: null,
    },
    inspectionDate: "",
    area: "",
    type: "",
    category: "",
    channel: "",
    state: "",
    city: "",
    contactName: "",
    contactPhone: "",
    contactEmail: "",
    quickLinks: [""],
    isPublished: false,
  });

  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load dropdown data
  useEffect(() => {
    dispatch(fetchAnnouncementsCategory());
    dispatch(fetchAnnouncementType());
    dispatch(fetchAllStates());
  }, [dispatch]);

  // Fetch cities when state is selected
  useEffect(() => {
    if (formData.state) {
      const selectedStateId = parseInt(formData.state);
      if (selectedStateId) {
        dispatch(fetchAllLocations(selectedStateId));
      }
    }
  }, [formData.state, dispatch]);

  // Parse business info from slug
  const businessInfo = BusinessRoutes.parseSlug(params.slug as string);

  const handleInputChange = (field: keyof AnnouncementFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, image: file }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleQuickLinkChange = (index: number, value: string) => {
    const newQuickLinks = [...formData.quickLinks];
    newQuickLinks[index] = value;
    setFormData(prev => ({ ...prev, quickLinks: newQuickLinks }));
  };

  const addQuickLink = () => {
    setFormData(prev => ({
      ...prev,
      quickLinks: [...prev.quickLinks, ""]
    }));
  };

  const removeQuickLink = (index: number) => {
    const newQuickLinks = formData.quickLinks.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, quickLinks: newQuickLinks }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.title || !formData.details) {
        showError("Please fill in all required fields.", "Validation Error");
        return;
      }

      // Validate date range if provided
      if (formData.dateRange.start && formData.dateRange.end && formData.dateRange.start > formData.dateRange.end) {
        showError("End date must be after start date.", "Validation Error");
        return;
      }

      // Create FormData for API submission
      const apiFormData = new FormData();

      // Add basic fields
      apiFormData.append('title', formData.title);
      apiFormData.append('details', formData.details);
      apiFormData.append('isPublish', formData.isPublished.toString());

      // Add date range if exists
      if (formData.dateRange.start) {
        const startDate = formData.dateRange.start.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        apiFormData.append('startDate', startDate);
      }
      if (formData.dateRange.end) {
        const endDate = formData.dateRange.end.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        apiFormData.append('endDate', endDate);
      }
      if (formData.inspectionDate) apiFormData.append('inspectionDate', formData.inspectionDate);
      if (formData.area) apiFormData.append('area', formData.area);
      if (formData.type) apiFormData.append('typeId', formData.type);
      if (formData.category) apiFormData.append('categoryId', formData.category);
      if (formData.channel) apiFormData.append('channelId', formData.channel);
      if (formData.state) apiFormData.append('stateId', formData.state);
      if (formData.city) apiFormData.append('cityId', formData.city);
      if (formData.contactName) apiFormData.append('contactName', formData.contactName);
      if (formData.contactPhone) apiFormData.append('contactPhone', formData.contactPhone);
      if (formData.contactEmail) apiFormData.append('contactEmail', formData.contactEmail);

      // Add image if exists
      if (formData.image) {
        apiFormData.append('image', formData.image);
      }

      // Add quick links as JSON string
      if (formData.quickLinks.length > 0 && formData.quickLinks.some(link => link.trim())) {
        const validLinks = formData.quickLinks.filter(link => link.trim());
        apiFormData.append('quickLinks', JSON.stringify(validLinks));
      }

      console.log("Submitting announcement data...");

      // Call API
      const result = await dispatch(createAnnouncement(apiFormData));

      if (createAnnouncement.fulfilled.match(result)) {
        showSuccess("Announcement created successfully!", "Success");

        // Navigate back to announcements list
        const announcementsUrl = BusinessRoutes.announcements(businessInfo.businessName, businessInfo.userId);
        router.push(announcementsUrl);
      } else {
        showError("Failed to create announcement. Please try again.", "Error");
      }

    } catch (error) {
      console.error("Error creating announcement:", error);
      showError("Failed to create announcement. Please try again.", "Error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    const announcementsUrl = BusinessRoutes.announcements(businessInfo.businessName, businessInfo.userId);
    router.push(announcementsUrl);
  };

  const handleBackToProfile = () => {
    const profileUrl = BusinessRoutes.profile(businessInfo.businessName, businessInfo.userId);
    router.push(profileUrl);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={handleBackToProfile}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Profile
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-xl font-semibold text-gray-900">Add New Announcement</h1>
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="publish-toggle" className="text-sm font-medium">
                Publish
              </Label>
              <Switch
                id="publish-toggle"
                checked={formData.isPublished}
                onCheckedChange={(checked) => handleInputChange('isPublished', checked)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Title and Image Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Title and Details */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Announcement Title</CardTitle>
                </CardHeader>
                <CardContent>
                  <Input
                    placeholder="Enter announcement title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="text-lg"
                    required
                  />
                </CardContent>
              </Card>





              <Card>
                <CardHeader>
                  <CardTitle>Announcement Details</CardTitle>
                  <p className="text-sm text-gray-600">(max 500 chars)</p>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Enter announcement details..."
                    value={formData.details}
                    onChange={(e) => handleInputChange('details', e.target.value)}
                    maxLength={500}
                    rows={6}
                    className="resize-none"
                    required
                  />
                  <div className="text-right text-sm text-gray-500 mt-2">
                    {formData.details.length}/500
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Image Upload */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Upload Image</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    {imagePreview ? (
                      <div className="space-y-4">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setImagePreview(null);
                            setFormData(prev => ({ ...prev, image: null }));
                          }}
                        >
                          Remove Image
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                        <div>
                          <Label htmlFor="image-upload" className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-500">Upload Image</span>
                            <span className="text-gray-500"> - png, jpg, jpeg</span>
                          </Label>
                          <Input
                            id="image-upload"
                            type="file"
                            accept="image/png,image/jpg,image/jpeg"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Date Selection Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Date Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <DateRangePicker
                    startDate={formData.dateRange.start}
                    endDate={formData.dateRange.end}
                    onDateChange={(dates: [Date | null, Date | null]) => {
                      handleInputChange('dateRange', {
                        start: dates[0],
                        end: dates[1]
                      });
                    }}
                  />
                </div>

                            <div className="flex items-center gap-2">
              <Label htmlFor="publish-toggle" className="text-sm font-medium">
                Publish
              </Label>
              <Switch
                id="publish-toggle"
                checked={formData.isPublished}
                onCheckedChange={(checked) => handleInputChange('isPublished', checked)}
              />
            </div>
                
              </div>
            </CardContent>
          </Card>

          {/* Category and Type Section */}
          <Card>
            <CardHeader>
              <CardTitle>Classification</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="area">Area</Label>
                  <Input
                    id="area"
                    placeholder="Enter area"
                    value={formData.area}
                    onChange={(e) => handleInputChange('area', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {annoucementType?.data?.map((type: any) => (
                        <SelectItem key={type.id} value={type.id.toString()}>
                          {type.typeName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {announcementCategories?.data?.map((category: any) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.categoryName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="channel">Channel</Label>
                  <Select value={formData.channel} onValueChange={(value) => handleInputChange('channel', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Channel" />
                    </SelectTrigger>
                    <SelectContent>
                      {channelsList?.map((channel: any) => (
                        <SelectItem key={channel.value} value={channel.value.toString()}>
                          {channel.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Location Info (if applicable)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <Label htmlFor="location">Location Address</Label>
                  <Textarea
                    id="location"
                    placeholder="Enter full address or location details"
                    rows={3}
                    className="mt-2"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Select value={formData.state} onValueChange={(value) => handleInputChange('state', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select State" />
                      </SelectTrigger>
                      <SelectContent>
                        {allStatesList?.map((state: any) => (
                          <SelectItem key={state.value} value={state.value.toString()}>
                            {state.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Select value={formData.city} onValueChange={(value) => handleInputChange('city', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select City" />
                      </SelectTrigger>
                      <SelectContent>
                        {allLocationsList?.map((city: any) => (
                          <SelectItem key={city.value} value={city.value.toString()}>
                            {city.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>


                  <div className="space-y-2">
                    <Label htmlFor="city">zipcode</Label>
                    <Select value={formData.city} onValueChange={(value) => handleInputChange('city', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Drop down" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="los-angeles">Los Angeles</SelectItem>
                        <SelectItem value="san-francisco">San Francisco</SelectItem>
                        <SelectItem value="houston">Houston</SelectItem>
                        <SelectItem value="miami">Miami</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>


                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Details Section */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="contact-name">First Name</Label>
                  <Input
                    id="contact-name"
                    placeholder="Contact person name"
                    value={formData.contactName}
                    onChange={(e) => handleInputChange('contactName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-name">Last Name</Label>
                  <Input
                    id="contact-name"
                    placeholder="Contact person name"
                    value={formData.contactName}
                    onChange={(e) => handleInputChange('contactName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-phone">Mobile</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      id="contact-phone"
                      placeholder="Phone number"
                      value={formData.contactPhone}
                      onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="contact-email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      id="contact-email"
                      type="email"
                      placeholder="Email address"
                      value={formData.contactEmail}
                      onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Links Section */}
          <Card>
            <CardHeader>
              <CardTitle>Announcement Quick Links</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {formData.quickLinks.map((link, index) => (
                  <div key={index} className="flex gap-2">
                    <div className="relative flex-1">
                      <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        placeholder={index === 0 ? "Link Text" : "Add Link"}
                        value={link}
                        onChange={(e) => handleQuickLinkChange(index, e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    {formData.quickLinks.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => removeQuickLink(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addQuickLink}
                  className="w-full"
                >
                  Add Link
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* News Release Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-600">News Release On NY Times</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Promote your announcement through our news release service
              </p>
              <Button type="button" variant="outline" className="w-full">
                Learn More About News Release
              </Button>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-center gap-4 pt-8">
            <Button
              type="button"
              variant="outline"
              onClick={handleBackToProfile}
              className="px-8"
            >
              Back To Profile
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="px-8 bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? "Creating..." : "Announcements"}
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleCancel}
              className="px-8"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddAnnouncementPage;
