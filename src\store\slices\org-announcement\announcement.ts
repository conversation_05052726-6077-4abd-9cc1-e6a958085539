
import { EachGalleryItem, HomeBanners } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

const initialState: any = {
  loading: false,
  error: "",
  orgAnnouncement: [],
  annoucementType: [],
  allAnnouncements: [],
  filteredAnnouncements: [],
  submitLoading: false,
  pinLoading: false,
  deleteLoading: false,
};

export const deleteAnnouncement = createAsyncThunk(
  "org/deleteAnnouncement",
  async (id: number, { fulfillWithValue, rejectWithValue }) => {
    console.log("Delete Announcement ID:", id);
    try {
      const token = JSON.parse(localStorage.getItem("token") || "");
      const baseUrl = process.env.NEXT_PUBLIC_USER_MANAGEMENT_DELETE_ANNOUNCEMENT;

      if (!baseUrl) {
        throw new Error("Delete announcement URL is not defined in environment variables.");
      }

      const url = `${baseUrl}/organization-announcement/${id}`;

      console.log("Deleting announcement with ID:", id);
      console.log("Delete API URL:", url);

      const response = await axios.delete(url, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });
      console.log("Delete response:", response);

      if (response?.data?.success === true) {
        return fulfillWithValue({ id, message: response.data.message });
      } else {
        return rejectWithValue(response?.data?.message || "Failed to delete announcement");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error:", error.message);
        return rejectWithValue(error.message);
      } else {
        return rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const fetchAllAnnouncements = createAsyncThunk(
  "org/allAnnouncements",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      console.log("Fetching all announcements from API")
      const token = JSON.parse(localStorage.getItem("token") || "");
      const baseUrl = process.env.NEXT_PUBLIC_USER_MANAGEMENT_ANNOUNCEMENT;
      const url = `${baseUrl}/organization-announcements`;
   
      if (!baseUrl) {
        throw new Error("Announcement API URL is not defined in environment variables.");
      }

      const response = await axios.get(url, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });

      console.log("All announcements API response:", response.data);

      if (response?.data?.success === true || response?.data) {
        // Return the full response data for now, we'll adjust based on actual API structure
        return fulfillWithValue(response.data.data);
      } else {
        return rejectWithValue(response?.data?.message || "Failed to fetch all announcements");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error fetching all announcements:", error.message);
        return rejectWithValue(error.message);
      } else {
        console.error("Unknown error fetching announcements:", error);
        return rejectWithValue("An unknown error occurred while fetching announcements");
      }
    }
  }
);

export const fetchAnnouncementType = createAsyncThunk(
  "org/announcementType",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const token = JSON.parse(localStorage.getItem("token") || "");
      const url = process.env.NEXT_PUBLIC_USER_MANAGEMENT_ANNOUNCEMENT_TYPE;

      if (!url) {
        throw new Error("Announcement category URL is not defined in environment variables.");
      }

      console.log("Fetching announcement categories from:", url);

      const response = await axios.get(url, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });

      console.log("Categories API response:", response.data);

      if (response?.data?.success === true || response?.data) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response?.data?.message || "Failed to fetch announcement categories");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error fetching categories:", error.message);
        return rejectWithValue(error.message);
      } else {
        console.error("Unknown error:", error);
        return rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const fetchAnnouncementsCategory = createAsyncThunk(
  "org/announcement",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const token = JSON.parse(localStorage.getItem("token") || "");
      const url = process.env.NEXT_PUBLIC_USER_MANAGEMENT_ANNOUNCEMENT_CATEGORY;

      if (!url) {
        throw new Error("Announcement type URL is not defined in environment variables.");
      }

      console.log("Fetching announcement types from:", url);

      const response = await axios.get(url, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });

      console.log("Types API response:", response.data);

      if (response?.data?.success === true || response?.data) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response?.data?.message || "Failed to fetch announcement types");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error fetching types:", error.message);
        return rejectWithValue(error.message);
      } else {
        console.error("Unknown error:", error);
        return rejectWithValue("An unknown error occurred");
      }
    }
  }
);

// New API call for filtered announcements
export const fetchFilteredAnnouncements = createAsyncThunk(
  "org/filteredAnnouncements",
  async (params: {
    startDate: string;
    endDate: string;
    isPublish: boolean;
    typeId?: number;
    categoryId?: number;
  }, { fulfillWithValue, rejectWithValue }) => {
    try {
      const token = JSON.parse(localStorage.getItem("token") || "");
      const baseUrl = process.env.NEXT_PUBLIC_USER_MANAGEMENT_ANNOUNCEMENT;

      if (!baseUrl) {
        throw new Error("Announcement API URL is not defined in environment variables.");
      }

      const apiUrl = `${baseUrl}/organization-announcements`;

      const queryParams = new URLSearchParams({
        startDate: params.startDate,
        endDate: params.endDate,
        isPublish: params.isPublish.toString(),
      });

      // Add optional parameters if provided
      if (params.typeId) {
        queryParams.append('typeId', params.typeId.toString());
      }
      if (params.categoryId) {
        queryParams.append('categoryId', params.categoryId.toString());
      }

      const url = `${apiUrl}?${queryParams.toString()}`;

      console.log("Filtered announcements API URL:", url);

      const response = await axios.get(url, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });

      if (response?.data?.success === true || response?.data) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response?.data?.message || "Failed to fetch filtered announcements");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error fetching filtered announcements:", error.message);
        return rejectWithValue(error.message);
      } else {
        console.error("Unknown error:", error);
        return rejectWithValue("An unknown error occurred");
      }
    }
  }
);

// Pin/Unpin announcement API call
export const pinAnnouncement = createAsyncThunk(
  "org/pinAnnouncement",
  async (params: { id: number; isPinned: number }, { fulfillWithValue, rejectWithValue }) => {
    try {
      const token = JSON.parse(localStorage.getItem("token") || "");
      const url = process.env.NEXT_PUBLIC_USER_MANAGEMENT_PIN_ANNOUNCEMENTS;

      if (!url) {
        throw new Error("Pin announcement URL is not defined in environment variables.");
      }

      const response = await axios.put(url, params, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });

      if (response?.data?.success === true || response?.data) {
        return fulfillWithValue({ ...params, success: true });
      } else {
        return rejectWithValue(response?.data?.message || "Failed to pin/unpin announcement");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error pinning announcement:", error.message);
        return rejectWithValue(error.message);
      } else {
        console.error("Unknown error:", error);
        return rejectWithValue("An unknown error occurred");
      }
    }
  }
);

const OrgAnnoucementSlice = createSlice({
  name: "orgAnnouncement",
  initialState,
  reducers: {

  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAnnouncementsCategory.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAnnouncementsCategory.fulfilled, (state, action) => {
        state.loading = false; 
        state.orgAnnouncement = action.payload;
      })
      .addCase(fetchAnnouncementsCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
       .addCase(fetchAnnouncementType.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAnnouncementType.fulfilled, (state, action) => {
        state.loading = false; 
        state.annoucementType = action.payload;
      })
      .addCase(fetchAnnouncementType.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAllAnnouncements.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAllAnnouncements.fulfilled, (state, action) => {
        state.loading = false;
        state.allAnnouncements = action.payload.data;
        // Clear filtered announcements when fetching all announcements
        state.filteredAnnouncements = [];
        console.log("last step", state.allAnnouncements)
      })
      .addCase(fetchAllAnnouncements.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(deleteAnnouncement.pending, (state) => {
        state.deleteLoading = true;
        state.error = "";
      })
      .addCase(deleteAnnouncement.fulfilled, (state, action) => {
        state.deleteLoading = false;
        // Remove the deleted item from both allAnnouncements and filteredAnnouncements
        if (state.allAnnouncements?.data) {
          state.allAnnouncements.data = state.allAnnouncements.data.filter(
            (item: any) => item.id !== action.payload.id
          );
        }
        if (state.filteredAnnouncements) {
          state.filteredAnnouncements = state.filteredAnnouncements.filter(
            (item: any) => item.id !== action.payload.id
          );
        }
      })
      .addCase(deleteAnnouncement.rejected, (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchFilteredAnnouncements.pending, (state) => {
        state.submitLoading = true;
        state.error = "";
      })
      .addCase(fetchFilteredAnnouncements.fulfilled, (state, action) => {
        state.submitLoading = false;
        state.filteredAnnouncements = action.payload.data;
      })
      .addCase(fetchFilteredAnnouncements.rejected, (state, action) => {
        state.submitLoading = false;
        state.error = action.payload as string;
      })
      .addCase(pinAnnouncement.pending, (state) => {
        state.pinLoading = true;
        state.error = "";
      })
      .addCase(pinAnnouncement.fulfilled, (state, action) => {
        state.pinLoading = false;
        // Update the pinned status in both allAnnouncements and filteredAnnouncements
        const { id, isPinned } = action.payload;

        // Update in allAnnouncements
        if (state.allAnnouncements?.data) {
          state.allAnnouncements.data = state.allAnnouncements.data.map((item: any) =>
            item.id === id ? { ...item, isPinned: isPinned === 1 } : item
          );
        }

        // Update in filteredAnnouncements
        if (state.filteredAnnouncements) {
          state.filteredAnnouncements = state.filteredAnnouncements.map((item: any) =>
            item.id === id ? { ...item, isPinned: isPinned === 1 } : item
          );
        }
      })
      .addCase(pinAnnouncement.rejected, (state, action) => {
        state.pinLoading = false;
        state.error = action.payload as string;
      })
  },
});

export const {} = OrgAnnoucementSlice.actions;
export default OrgAnnoucementSlice.reducer;
