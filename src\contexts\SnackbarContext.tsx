"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import Snackbar, { SnackbarType } from "@/components/ui/Snackbar";

interface SnackbarItem {
  id: string;
  type: SnackbarType;
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface SnackbarContextType {
  showSnackbar: (snackbar: Omit<SnackbarItem, "id">) => void;
  showSuccess: (message: string, title?: string) => void;
  showError: (message: string, title?: string) => void;
  showInfo: (message: string, title?: string) => void;
  showWarning: (message: string, title?: string) => void;
  hideSnackbar: (id: string) => void;
  hideAllSnackbars: () => void;
}

const SnackbarContext = createContext<SnackbarContextType | undefined>(undefined);

export const useSnackbar = () => {
  const context = useContext(SnackbarContext);
  if (!context) {
    throw new Error("useSnackbar must be used within a SnackbarProvider");
  }
  return context;
};

interface SnackbarProviderProps {
  children: React.ReactNode;
  maxSnackbars?: number;
}

export const SnackbarProvider: React.FC<SnackbarProviderProps> = ({
  children,
  maxSnackbars = 5,
}) => {
  const [snackbars, setSnackbars] = useState<SnackbarItem[]>([]);

  const generateId = () => {
    return `snackbar-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  const showSnackbar = useCallback(
    (snackbar: Omit<SnackbarItem, "id">) => {
      const id = generateId();
      const newSnackbar: SnackbarItem = {
        id,
        duration: 5000,
        ...snackbar,
      };

      setSnackbars((prev) => {
        const updated = [newSnackbar, ...prev];
        // Limit the number of snackbars
        return updated.slice(0, maxSnackbars);
      });
    },
    [maxSnackbars]
  );

  const showSuccess = useCallback(
    (message: string, title?: string) => {
      showSnackbar({
        type: "success",
        message,
        title: title || "Success",
      });
    },
    [showSnackbar]
  );

  const showError = useCallback(
    (message: string, title?: string) => {
      showSnackbar({
        type: "error",
        message,
        title: title || "Error",
        duration: 7000, // Longer duration for errors
      });
    },
    [showSnackbar]
  );

  const showInfo = useCallback(
    (message: string, title?: string) => {
      showSnackbar({
        type: "info",
        message,
        title: title || "Info",
      });
    },
    [showSnackbar]
  );

  const showWarning = useCallback(
    (message: string, title?: string) => {
      showSnackbar({
        type: "warning",
        message,
        title: title || "Warning",
        duration: 6000,
      });
    },
    [showSnackbar]
  );

  const hideSnackbar = useCallback((id: string) => {
    setSnackbars((prev) => prev.filter((snackbar) => snackbar.id !== id));
  }, []);

  const hideAllSnackbars = useCallback(() => {
    setSnackbars([]);
  }, []);

  const value: SnackbarContextType = {
    showSnackbar,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    hideSnackbar,
    hideAllSnackbars,
  };

  return (
    <SnackbarContext.Provider value={value}>
      {children}
      
      {/* Render Snackbars */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {snackbars.map((snackbar, index) => (
          <div
            key={snackbar.id}
            style={{
              transform: `translateY(${index * 10}px)`,
              zIndex: 50 - index,
            }}
          >
            <Snackbar
              {...snackbar}
              onClose={hideSnackbar}
            />
          </div>
        ))}
      </div>
    </SnackbarContext.Provider>
  );
};
