import React, { useState } from "react";
import AnnouncementCard from "./announcementCard";
import { useDispatch } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllAnnouncements,
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
} from "@/store/slices/org-announcement/announcement";
import { useSelector } from "react-redux";
import CustomDropdown from "../common/DropDown";
import DateRangePicker from "../common/DatePicker";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Calendar, Filter, Search, Sparkles, Plus } from "lucide-react";


const AnnouncementsDashboard: React.FC = () => {
  const [announcements, setAnnouncements] = useState();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(false);

  // Filter logic
  const filteredAnnouncements = announcements.filter((announcement) => {
    const matchesSearch = announcement.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === "all" || announcement.type.typeName === selectedType;
    const matchesCategory = selectedCategory === "all" || announcement.category.categoryName === selectedCategory;
    const matchesStatus = selectedStatus === "all" || 
      (selectedStatus === "active" && announcement.isActive) ||
      (selectedStatus === "inactive" && !announcement.isActive);

    return matchesSearch && matchesType && matchesCategory && matchesStatus;
  });

  // Get unique options for filters
  const types = [...new Set(announcements.map(a => a.type.typeName))];
  const categories = [...new Set(announcements.map(a => a.category.categoryName))];

  const handleDelete = async (id: number) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    setAnnouncements(prev => prev.filter(a => a.id !== id));
    setIsLoading(false);
  };

  const handlePin = async (id: number, isPinned: boolean) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 300));
    setAnnouncements(prev => 
      prev.map(a => a.id === id ? { ...a, isPinned } : a)
    );
    setIsLoading(false);
  };

  const handleEdit = (id: number) => {
    console.log("Edit announcement:", id);
    // Add edit logic here
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setSelectedType("all");
    setSelectedCategory("all");
    setSelectedStatus("all");
  };

  return (
    <div className="min-h-screen bg-dashboard-bg">
      {/* Header */}
      <div className="bg-surface border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-on-surface">
                  Announcements
                </h1>
                <p className="text-on-surface-variant mt-1">
                  Manage and organize your organization announcements
                </p>
              </div>
            </div>
            
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
              <Plus className="w-4 h-4 mr-2" />
              New Announcement
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Filters Section */}
        <div className="bg-surface rounded-lg border border-border shadow-sm">
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Filter className="w-5 h-5 text-primary" />
                <h2 className="text-lg font-semibold text-on-surface">Filters</h2>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleClearFilters}
                className="text-on-surface-variant"
              >
                Clear All
              </Button>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {/* Search */}
              {/* <div className="lg:col-span-2 space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-on-surface-variant" />
                  <Input
                    placeholder="Search announcements..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div> */}

              {/* Type Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Type
                </label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {types.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Category
                </label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Status
                </label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="bg-surface rounded-lg border border-border shadow-sm">
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-success/10 rounded-lg flex items-center justify-center">
                  <span className="text-success font-semibold">📢</span>
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-on-surface">
                    All Announcements
                  </h2>
                  <p className="text-sm text-on-surface-variant">
                    {filteredAnnouncements.length} of {announcements.length} announcements
                  </p>
                </div>
              </div>
              
              <div className="bg-surface-variant px-3 py-1 rounded-md">
                <span className="text-sm font-medium text-on-surface">
                  {filteredAnnouncements.length} Results
                </span>
              </div>
            </div>
          </div>

          <div className="p-6">
            {filteredAnnouncements.length > 0 ? (
              <div className="space-y-4">
                {filteredAnnouncements.map((announcement) => (
                  <AnnouncementCard 
                    key={announcement.id} 
                    data={announcement}
                    onDelete={handleDelete}
                    onPin={handlePin}
                    onEdit={handleEdit}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-surface-variant rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-4xl opacity-50">📭</span>
                </div>
                <h3 className="text-lg font-medium text-on-surface mb-2">
                  No announcements found
                </h3>
                <p className="text-on-surface-variant max-w-md mx-auto">
                  {searchTerm || selectedType !== "all" || selectedCategory !== "all" || selectedStatus !== "all"
                    ? "No announcements match your current filters. Try adjusting your search criteria."
                    : "No announcements have been created yet. Create your first announcement to get started."
                  }
                </p>
                {(searchTerm || selectedType !== "all" || selectedCategory !== "all" || selectedStatus !== "all") && (
                  <Button 
                    variant="outline" 
                    onClick={handleClearFilters}
                    className="mt-4"
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementsDashboard;