import React, { useEffect, useState } from "react";
import AnnouncementCard from "./announcementCard";
import { useDispatch } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllAnnouncements,
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
  fetchFilteredAnnouncements,
  deleteAnnouncement,
} from "@/store/slices/org-announcement/announcement";
import { useSelector } from "react-redux";
import CustomDropdown from "../common/DropDown";
import DateRangePicker from "../common/DatePicker";
import { Button } from "../ui/button";
import { Calendar, Filter, Search, Sparkles } from "lucide-react";

const Index: React.FC = () => {
  const [selectedType, setSelectedType] = useState<string>("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("Publish");
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  const {
    orgAnnouncement,
    annoucementType,
    allAnnouncements,
    filteredAnnouncements,
    submitLoading,
    error,
  } = useSelector((state: RootState) => state.announcementDashboard);
  const dispatch = useDispatch<AppDispatch>();

  const handleAnnouncementTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedType(e.target.value);
  };

  const handleCategoryTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedCategory(e.target.value);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStatus(e.target.value);
  };

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    setStartDate(dates[0]);
    setEndDate(dates[1]);
  };

  const statusOptions = [
    {
      label: "Publish",
      value: "Publish",
    },
    {
      label: "Unpublish",
      value: "Unpublish",
    },
  ];

  useEffect(() => {
    dispatch(fetchAnnouncementsCategory());
    dispatch(fetchAnnouncementType());
    dispatch(fetchAllAnnouncements());
  }, []);

  const handleDelete = async (id: number) => {
    await dispatch(deleteAnnouncement(id.toString()));
    dispatch(fetchAllAnnouncements());
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!startDate || !endDate) {
      alert("Please select both start and end dates");
      return;
    }

    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    };

    const params = {
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      isPublish: selectedStatus === "Publish",
    };

    try {
      await dispatch(fetchFilteredAnnouncements(params));
      console.log("Filtered announcements fetched successfully");
    } catch (error) {
      console.error("Error fetching filtered announcements:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-8 px-4">
      {/* Header Section */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Announcements Dashboard
            </h1>
          </div>
          <p className="text-gray-600 text-lg">Manage and filter your organization announcements</p>
        </div>
      </div>

      {/* Filter Form */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6">
            <div className="flex items-center gap-3">
              <Filter className="w-6 h-6 text-white" />
              <h2 className="text-2xl font-bold text-white">Filter Announcements</h2>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Announcement Type */}
              <div className="space-y-3">
                <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Announcement Type
                </label>
                <div className="relative">
                  <CustomDropdown
                    options={annoucementType?.data?.map((item: any) => item?.typeName) || []}
                    selectedValue={selectedType}
                    handleChange={handleAnnouncementTypeChange}
                  />
                </div>
              </div>

              {/* Date Range */}
              <div className="space-y-3">
                <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  Date Range
                </label>
                <div className="relative">
                  <DateRangePicker
                    startDate={startDate}
                    endDate={endDate}
                    onDateChange={handleDateChange}
                  />
                </div>
              </div>

              {/* Announcement Category */}
              <div className="space-y-3">
                <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  Category
                </label>
                <div className="relative">
                  <CustomDropdown
                    options={orgAnnouncement?.data?.map((item: any) => item?.categoryName) || []}
                    selectedValue={selectedCategory}
                    handleChange={handleCategoryTypeChange}
                  />
                </div>
              </div>

              {/* Status */}
              <div className="space-y-3">
                <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  Status
                </label>
                <div className="relative">
                  <CustomDropdown
                    options={statusOptions?.map((item: any) => item?.value) || []}
                    selectedValue={selectedStatus}
                    handleChange={handleStatusChange}
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-8 flex justify-center">
              <Button
                type="submit"
                disabled={submitLoading}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-2"
              >
                {submitLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Search className="w-5 h-5" />
                    Search Announcements
                  </>
                )}
              </Button>
            </div>

            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm font-medium">{error}</p>
              </div>
            )}
          </form>
        </div>
      </div>

      {/* Announcements Results */}
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">📢</span>
                </div>
                <h2 className="text-2xl font-bold text-white">
                  {filteredAnnouncements?.length > 0 ? 'Filtered Results' : 'All Announcements'}
                </h2>
              </div>
              <div className="bg-white bg-opacity-20 px-4 py-2 rounded-lg">
                <span className="text-white font-semibold">
                  {(filteredAnnouncements?.length > 0 ? filteredAnnouncements : allAnnouncements?.data)?.length || 0} Results
                </span>
              </div>
            </div>
          </div>

          <div className="p-8">
            {(filteredAnnouncements?.length > 0 ? filteredAnnouncements : allAnnouncements?.data)?.length > 0 ? (
              <div className="space-y-6">
                {(filteredAnnouncements?.length > 0 ? filteredAnnouncements : allAnnouncements?.data)?.map((item: any) => (
                  <AnnouncementCard key={item.id} data={item} onDelete={handleDelete} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-4xl">📭</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">No announcements found</h3>
                <p className="text-gray-500">Try adjusting your filters or create a new announcement.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
