import React, { useState, useEffect } from "react";
import AnnouncementCard from "./announcementCard";
import { useDispatch } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllAnnouncements,
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
  fetchFilteredAnnouncements,
} from "@/store/slices/org-announcement/announcement";
import { useSelector } from "react-redux";
import CustomDropdown from "../common/DropDown";
import DateRangePicker from "../common/DatePicker";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Calendar, Filter, Search, Sparkles, Plus } from "lucide-react";


  const statusOptions = [
    {
      label: "Publish",
      value: "Publish",
    },
    {
      label: "Unpublish",
      value: "Unpublish",
    },
  ];


const AnnouncementsDashboard: React.FC = () => {
  // Redux state
  const {
    allAnnouncements,
    annoucementType,
    orgAnnouncement: announcementCategories,
    filteredAnnouncements,
    loading,
    submitLoading,
    error,
  } = useSelector((state: RootState) => state.announcementDashboard);
  const dispatch = useDispatch<AppDispatch>();

  // Local state for filtering
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [dateRange, setDateRange] = useState<{start: Date | null, end: Date | null}>({
    start: null,
    end: null
  });
  const [isLoading, setIsLoading] = useState(false);

  // Get announcements data
  const announcements = allAnnouncements.data || [];

  // Filter logic for local filtering (when no API filter is applied)
  const localFilteredAnnouncements = announcements.filter((announcement: any) => {
    const matchesSearch = announcement.title?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === "all" || announcement.type?.typeName === selectedType;
    const matchesCategory = selectedCategory === "all" || announcement.category?.categoryName === selectedCategory;
    const matchesStatus = selectedStatus === "all" ||
      (selectedStatus === "true" && announcement.isPublish) ||
      (selectedStatus === "false" && !announcement.isPublish);

    return matchesSearch && matchesType && matchesCategory && matchesStatus;
  });

  // Get dropdown options from API data
  const types = annoucementType.data || [];
  console.log(annoucementType,"annoucementType",announcementCategories)
  const categories = announcementCategories.data || [];

  // Load announcements on component mount
  useEffect(() => {
    dispatch(fetchAllAnnouncements());
    dispatch(fetchAnnouncementsCategory());
    dispatch(fetchAnnouncementType());
  }, [dispatch]);

  // Handle filter submission
  const handleFilterSubmit = async () => {
    if (!dateRange.start || !dateRange.end) {
      // If no dates selected, show all announcements
      dispatch(fetchAllAnnouncements());
      return;
    }

    const formatDate = (date: Date) => {
      // Format as DD-MM-YYYY as required by API
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    };

    // Find the selected type and category IDs
    const selectedTypeObj = types.find((type: any) => type.typeName === selectedType);
    const selectedCategoryObj = categories.find((category: any) => category.categoryName === selectedCategory);

    const params: any = {
      startDate: formatDate(dateRange.start),
      endDate: formatDate(dateRange.end),
      isPublish: selectedStatus === "true" ? true : selectedStatus === "false" ? false : true,
    };

    // Add typeId and categoryId if selected
    if (selectedTypeObj && selectedType !== "all") {
      params.typeId = selectedTypeObj.id;
    }
    if (selectedCategoryObj && selectedCategory !== "all") {
      params.categoryId = selectedCategoryObj.id;
    }

    console.log("Filter params:", params);

    try {
      await dispatch(fetchFilteredAnnouncements(params));
    } catch (error) {
      console.error("Error fetching filtered announcements:", error);
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSearchTerm("");
    setSelectedType("all");
    setSelectedCategory("all");
    setSelectedStatus("all");
    setDateRange({ start: null, end: null });
    dispatch(fetchAllAnnouncements());
  };

  return (
    <div className="min-h-screen bg-dashboard-bg">
      {/* Header */}
      <div className="bg-surface border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-on-surface">
                  Announcements
                </h1>
                <p className="text-on-surface-variant mt-1">
                  Manage and organize your organization announcements
                </p>
              </div>
            </div>
            
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
              <Plus className="w-4 h-4 mr-2" />
              New Announcement
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Filters Section */}
        <div className="bg-surface rounded-lg border border-border shadow-sm">
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Filter className="w-5 h-5 text-primary" />
                <h2 className="text-lg font-semibold text-on-surface">Filters</h2>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleClearFilters}
                className="text-on-surface-variant"
              >
                Clear All
              </Button>
            </div>
          </div>

          <div className="p-6">
            {/* Search Bar */}
            <div className="mb-6">
              <label className="text-sm font-medium text-on-surface mb-2 block">
                Search Announcements
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-on-surface-variant" />
                <input
                  type="text"
                  placeholder="Search announcements..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

              {/* Type Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Type
                </label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {types.map((type: any) => (
                      <SelectItem key={type.id || type.typeName} value={type.typeName || type.name}>
                        {type.typeName || type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Category
                </label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category: any) => (
                      <SelectItem key={category.id || category.categoryName} value={category.categoryName || category.name}>
                        {category.categoryName || category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Date Range */}
              <div className="space-y-2 lg:col-span-2">
                <label className="text-sm font-medium text-on-surface">
                  Date Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <input
                      type="date"
                      placeholder="Start Date"
                      value={dateRange.start ? dateRange.start.toISOString().split('T')[0] : ''}
                      onChange={(e) => setDateRange(prev => ({
                        ...prev,
                        start: e.target.value ? new Date(e.target.value) : null
                      }))}
                      className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                    />
                  </div>
                  <div>
                    <input
                      type="date"
                      placeholder="End Date"
                      value={dateRange.end ? dateRange.end.toISOString().split('T')[0] : ''}
                      onChange={(e) => setDateRange(prev => ({
                        ...prev,
                        end: e.target.value ? new Date(e.target.value) : null
                      }))}
                      className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                    />
                  </div>
                </div>
                <div className="text-xs text-gray-500 flex justify-between">
                  <span>Start Date</span>
                  <span>End Date</span>
                </div>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Status
                </label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="true">Publish</SelectItem>
                    <SelectItem value="false">Unpublish</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Filter Actions */}
            <div className="mt-6 flex gap-3">
              <Button
                onClick={handleFilterSubmit}
                disabled={submitLoading}
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                {submitLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Filtering...
                  </>
                ) : (
                  <>
                    <Filter className="w-4 h-4 mr-2" />
                    Apply Filters
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={handleClearFilters}
                className="text-on-surface-variant"
              >
                Clear All
              </Button>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="bg-surface rounded-lg border border-border shadow-sm">
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-success/10 rounded-lg flex items-center justify-center">
                  <span className="text-success font-semibold">📢</span>
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-on-surface">
                    All Announcements
                  </h2>
                  <p className="text-sm text-on-surface-variant">
                    {filteredAnnouncements.length} of {announcements.length} announcements
                  </p>
                </div>
              </div>
              
              <div className="bg-surface-variant px-3 py-1 rounded-md">
                <span className="text-sm font-medium text-on-surface">
                  {filteredAnnouncements.length} Results
                </span>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Show announcements */}
            {loading ? (
              <div className="text-center py-12">
                <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">Loading announcements...</p>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-4xl">❌</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Announcements</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <Button onClick={() => dispatch(fetchAllAnnouncements())} className="bg-blue-600 hover:bg-blue-700">
                  Try Again
                </Button>
              </div>
            ) : (
              // Determine which announcements to show
              (() => {
                // If we have API filtered results (from date/status filter), show those
                if (filteredAnnouncements && filteredAnnouncements.length >= 0) {
                  const displayAnnouncements = filteredAnnouncements;

                  // Apply local filters (search, type, category) to API results
                  const finalAnnouncements = displayAnnouncements.filter((announcement: any) => {
                    const matchesSearch = !searchTerm || announcement.title?.toLowerCase().includes(searchTerm.toLowerCase());
                    const matchesType = selectedType === "all" || announcement.type?.typeName === selectedType;
                    const matchesCategory = selectedCategory === "all" || announcement.category?.categoryName === selectedCategory;
                    return matchesSearch && matchesType && matchesCategory;
                  });

                  return finalAnnouncements.length > 0 ? (
                    <div className="space-y-4">
                      {finalAnnouncements.map((announcement: any) => (
                        <AnnouncementCard
                          key={announcement.id}
                          data={announcement}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-4xl opacity-50">🔍</span>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No matching announcements</h3>
                      <p className="text-gray-600">Try adjusting your search criteria.</p>
                    </div>
                  );
                }

                // Otherwise, show locally filtered announcements
                const displayAnnouncements = (searchTerm || selectedType !== "all" || selectedCategory !== "all" || selectedStatus !== "all")
                  ? localFilteredAnnouncements
                  : announcements;

                return displayAnnouncements.length > 0 ? (
                  <div className="space-y-4">
                    {displayAnnouncements.map((announcement: any) => (
                      <AnnouncementCard
                        key={announcement.id}
                        data={announcement}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-4xl opacity-50">📭</span>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No announcements found
                    </h3>
                    <p className="text-gray-600 max-w-md mx-auto">
                      No announcements have been created yet. Create your first announcement to get started.
                    </p>
                  </div>
                );
              })()
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementsDashboard;