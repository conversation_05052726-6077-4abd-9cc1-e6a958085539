import React, { useState, useEffect } from "react";
import AnnouncementCard from "./announcementCard";
import { useDispatch } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllAnnouncements,
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
} from "@/store/slices/org-announcement/announcement";
import { useSelector } from "react-redux";
import CustomDropdown from "../common/DropDown";
import DateRangePicker from "../common/DatePicker";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Calendar, Filter, Search, Sparkles, Plus } from "lucide-react";


  const statusOptions = [
    {
      label: "Publish",
      value: "Publish",
    },
    {
      label: "Unpublish",
      value: "Unpublish",
    },
  ];


const AnnouncementsDashboard: React.FC = () => {
  // Redux state
  const {
    allAnnouncements,
    loading,
    error,
  } = useSelector((state: RootState) => state.announcementDashboard);
  const dispatch = useDispatch<AppDispatch>();

  // Local state for filtering
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(false);

  // Get announcements data
  const announcements = allAnnouncements || [];

  // Filter logic
  const filteredAnnouncements = announcements.filter((announcement: any) => {
    const matchesSearch = announcement.title?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === "all" || announcement.type?.typeName === selectedType;
    const matchesCategory = selectedCategory === "all" || announcement.category?.categoryName === selectedCategory;
    const matchesStatus = selectedStatus === "all" ||
      (selectedStatus === "active" && announcement.isActive) ||
      (selectedStatus === "inactive" && !announcement.isActive);

    return matchesSearch && matchesType && matchesCategory && matchesStatus;
  });

  // Get unique options for filters
  const types = Array.from(new Set(announcements.map((a: any) => a.type?.typeName).filter(Boolean))) as string[];
  const categories = Array.from(new Set(announcements.map((a: any) => a.category?.categoryName).filter(Boolean))) as string[];

  // Load announcements on component mount
  useEffect(() => {
    dispatch(fetchAllAnnouncements());
    dispatch(fetchAnnouncementsCategory());
    dispatch(fetchAnnouncementType());
  }, [dispatch]);

  const handleClearFilters = () => {
    setSearchTerm("");
    setSelectedType("all");
    setSelectedCategory("all");
    setSelectedStatus("all");
  };

  return (
    <div className="min-h-screen bg-dashboard-bg">
      {/* Header */}
      <div className="bg-surface border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-on-surface">
                  Announcements
                </h1>
                <p className="text-on-surface-variant mt-1">
                  Manage and organize your organization announcements
                </p>
              </div>
            </div>
            
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
              <Plus className="w-4 h-4 mr-2" />
              New Announcement
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Filters Section */}
        <div className="bg-surface rounded-lg border border-border shadow-sm">
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Filter className="w-5 h-5 text-primary" />
                <h2 className="text-lg font-semibold text-on-surface">Filters</h2>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleClearFilters}
                className="text-on-surface-variant"
              >
                Clear All
              </Button>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {/* Search */}
              {/* <div className="lg:col-span-2 space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-on-surface-variant" />
                  <Input
                    placeholder="Search announcements..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div> */}

              {/* Type Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Type
                </label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {types.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Category
                </label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-on-surface">
                  Status
                </label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="false">All Status</SelectItem>
                    <SelectItem value="true">Publish</SelectItem>
                    <SelectItem value="false">Unpublish</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="bg-surface rounded-lg border border-border shadow-sm">
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-success/10 rounded-lg flex items-center justify-center">
                  <span className="text-success font-semibold">📢</span>
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-on-surface">
                    All Announcements
                  </h2>
                  <p className="text-sm text-on-surface-variant">
                    {filteredAnnouncements.length} of {announcements.length} announcements
                  </p>
                </div>
              </div>
              
              <div className="bg-surface-variant px-3 py-1 rounded-md">
                <span className="text-sm font-medium text-on-surface">
                  {filteredAnnouncements.length} Results
                </span>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Show announcements */}
            {loading ? (
              <div className="text-center py-12">
                <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">Loading announcements...</p>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-4xl">❌</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Announcements</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <Button onClick={() => dispatch(fetchAllAnnouncements())} className="bg-blue-600 hover:bg-blue-700">
                  Try Again
                </Button>
              </div>
            ) : (searchTerm || selectedType !== "all" || selectedCategory !== "all" || selectedStatus !== "all") ? (
              // Show filtered results
              filteredAnnouncements.length > 0 ? (
                <div className="space-y-4">
                  {filteredAnnouncements.map((announcement: any) => (
                    <AnnouncementCard
                      key={announcement.id}
                      data={announcement}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-4xl opacity-50">🔍</span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No matching announcements</h3>
                  <p className="text-gray-600">Try adjusting your search criteria.</p>
                </div>
              )
            ) : (
              // Show all announcements
              announcements.length > 0 ? (
                <div className="space-y-4">
                  {announcements.map((announcement: any) => (
                    <AnnouncementCard
                      key={announcement.id}
                      data={announcement}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-4xl opacity-50">📭</span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No announcements found
                  </h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    No announcements have been created yet. Create your first announcement to get started.
                  </p>
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementsDashboard;