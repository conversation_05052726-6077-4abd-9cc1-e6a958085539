"use client";

import { useParams } from "next/navigation";
import { useEffect } from "react";
import SportsBusiness from "@/components/announcement-dashboard/index";
import { useRoleBasedRoute } from "@/hooks/useRoleBasedRouting";
import ClientGuard from "@/components/ClientGuard";

const BusinessAnnouncementsPage = () => {
  const { isAuthenticated } = useRoleBasedRoute();
  const params = useParams();
  const slug = params.slug as string;

  // Extract business name and user ID from slug
  // Expected format: "BusinessName-UserID"
  const extractBusinessInfo = (slug: string) => {
    if (!slug) return { businessName: "", userId: "" };
    
    const lastDashIndex = slug.lastIndexOf('-');
    if (lastDashIndex === -1) return { businessName: slug, userId: "" };
    
    const businessName = slug.substring(0, lastDashIndex);
    const userId = slug.substring(lastDashIndex + 1);
    
    return { businessName, userId };
  };

  const { businessName, userId } = extractBusinessInfo(slug);

  useEffect(() => {
    // You can use businessName and userId here for any specific logic
    console.log("Business Name:", businessName);
    console.log("User ID:", userId);
  }, [businessName, userId]);

  if (!isAuthenticated) return null;

  return (
    <ClientGuard allowedRoles={[4]}>
      <div className="min-h-screen">
        {/* Optional: Display business info */}
        <div className="bg-white border-b border-gray-200 px-4 py-2">
          <div className="max-w-7xl mx-auto">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Business:</span> {businessName} 
              <span className="mx-2">•</span>
              <span className="font-medium">ID:</span> {userId}
              <span className="mx-2">•</span>
              <span className="text-blue-600 font-medium">Announcements</span>
            </div>
          </div>
        </div>
        
        {/* Announcements Dashboard Component */}
        <SportsBusiness />
      </div>
    </ClientGuard>
  );
};

export default BusinessAnnouncementsPage;
